# 🚀 SES - Solana Enhanced Scanner .gitignore

# Environment files
.env
.env.local
.env.*.local

# Python
__pycache__/
*.py[cod]
*$py.class
venv/
env/
ENV/
.venv/
.pytest_cache/
.coverage
htmlcov/

# Rust
rust/target/
rust/Cargo.lock
**/*.rs.bk
*.pdb

# Database
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/
*.out

# OS generated files
.DS_Store
.DS_Store?
._*
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Outputs (keep structure, ignore content)
outputs/visualizations/*.png
outputs/visualizations/*.jpg
outputs/visualizations/*.svg
outputs/reports/*.pdf
outputs/reports/*.html
outputs/exports/*.csv
outputs/exports/*.json

# Temporary files
tmp/
temp/
*.tmp
*.temp
