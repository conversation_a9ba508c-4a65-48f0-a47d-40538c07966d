# 📊 Trading Data Analysis - SolanaTracker Integration

## Overview

This document explains how to use the new SolanaTracker API integration to collect detailed trading data and enhance our machine learning models for token migration prediction.

## 🔥 Key Features Added

### 1. Detailed Trading Data Collection
- **Individual Trade Records**: Every buy/sell transaction with precise timing
- **Wallet-Level Analysis**: Track individual trader behavior patterns
- **Volume Patterns**: Detailed volume analysis across different time periods
- **Price Impact**: Understanding how trades affect token prices

### 2. Advanced ML Features
- **Trading Velocity**: Trades per hour since token creation
- **Buy/Sell Ratios**: Market sentiment indicators
- **Whale vs Retail Analysis**: Distribution of trading activity
- **Momentum Scoring**: Recent activity vs historical patterns

### 3. Migration Prediction Enhancement
- **Real-time Analysis**: Live migration probability scoring
- **Risk Assessment**: Automated risk factor identification
- **Recommendation Engine**: Buy/Hold/Sell recommendations

## 📈 Data Structure

### Token Trades Table
```sql
CREATE TABLE token_trades (
    id UUID PRIMARY KEY,
    token_address VARCHAR(44) NOT NULL,
    tx_hash VARCHAR(88) NOT NULL UNIQUE,
    trade_time TIMESTAMP WITH TIME ZONE NOT NULL,
    trade_type VARCHAR(10) NOT NULL, -- 'buy' or 'sell'
    wallet_address VARCHAR(44) NOT NULL,
    token_amount DECIMAL(30,6) NOT NULL,
    price_usd DECIMAL(20,10) NOT NULL,
    volume_usd DECIMAL(20,6) NOT NULL,
    volume_sol DECIMAL(20,9),
    program VARCHAR(50), -- 'pump', etc.
    pools JSONB, -- Pool addresses involved
    trade_size_category VARCHAR(20), -- 'micro', 'small', 'medium', 'large', 'whale'
    raw_data JSONB -- Complete API response
);
```

### Enhanced ML Features
The `token_migration_features` table now includes:

- **Volume Metrics**: `total_trade_volume_usd`, `avg_trade_size_usd`, `median_trade_size_usd`
- **Trading Dynamics**: `buy_sell_ratio`, `buy_volume_ratio`, `net_volume_flow`
- **Participant Analysis**: `unique_buyers`, `unique_sellers`, `whale_trades_count`
- **Time-based Patterns**: `trades_last_1h`, `volume_last_24h`, `trading_velocity`
- **Market Behavior**: `momentum_score`, `retail_participation_ratio`

## 🚀 Usage Examples

### 1. Collect Trading Data for a Token
```bash
curl "http://localhost:3001/trades?token_address=CgDencV766uQUArDFp1GtUjubCcQYreRqBm5xmyEpump&limit=100&update_features=true"
```

### 2. Get Migration Analysis
```sql
SELECT * FROM analyze_trading_patterns('CgDencV766uQUArDFp1GtUjubCcQYreRqBm5xmyEpump');
```

### 3. Find Top Migration Candidates
```sql
SELECT * FROM get_migration_candidates(20);
```

### 4. Analyze Trading Patterns
```sql
SELECT 
    token_address,
    total_trade_volume_usd,
    buy_sell_ratio,
    unique_buyers + unique_sellers as total_traders,
    momentum_score,
    curve_percentage
FROM token_migration_features 
WHERE did_migrate = FALSE 
AND total_trade_volume_usd > 10000
ORDER BY momentum_score DESC;
```

## 🎯 Key Insights from Trading Data

### Migration Indicators
1. **Volume Threshold**: Tokens with >$50k total volume have higher migration probability
2. **Trader Diversity**: >50 unique traders indicates healthy distribution
3. **Curve Completion**: >80% bonding curve completion is critical
4. **Buy/Sell Balance**: Healthy ratio (0.8-1.5) suggests sustainable growth
5. **Momentum**: Recent activity 1.5x+ historical average indicates momentum

### Risk Factors
1. **Whale Dominance**: >70% whale trades indicates manipulation risk
2. **Low Volume**: <$1k total volume suggests lack of interest
3. **Rapid Dumps**: High sell volume spikes indicate potential rugs
4. **Creator History**: Low creator success rate (<30%) is concerning

## 📊 ML Model Enhancement

### New Features for Prediction
```python
# Key features for migration prediction
features = [
    'total_trade_volume_normalized',
    'buy_sell_ratio_normalized', 
    'wallet_diversity_normalized',
    'trading_velocity_normalized',
    'momentum_score_normalized',
    'curve_completion_normalized',
    'creator_success_rate_normalized',
    'retail_participation_normalized'
]
```

### Feature Engineering
- **Normalization**: All features scaled to 0-1 range
- **Time Windows**: Multiple time horizons (1h, 6h, 24h)
- **Ratios**: Relative metrics more predictive than absolute values
- **Momentum**: Recent vs historical comparisons

## 🔧 Implementation Guide

### 1. Setup Database Schema
```bash
# Apply the enhanced schema
psql $DATABASE_URL -f sql/schemas/ml_features_schema.sql
psql $DATABASE_URL -f sql/functions/solanatracker_integration.sql
```

### 2. Start the Enhanced Server
```bash
cd rust
SOLANA_TRACKER_API_KEY="your-api-key" cargo run
```

### 3. Test the Integration
```bash
./scripts/test_trading_integration.sh --setup-db
```

### 4. Collect Data for Analysis
```bash
# Collect trades for multiple tokens
for token in token1 token2 token3; do
    curl "http://localhost:3001/trades?token_address=$token&limit=200&update_features=true"
    sleep 1
done
```

## 📈 Analysis Workflows

### Daily Migration Scanning
1. **Collect Recent Trades**: Update trading data for active tokens
2. **Calculate Features**: Run feature extraction for all tokens
3. **Score Migration Probability**: Use ML model to score tokens
4. **Generate Alerts**: Identify high-probability migration candidates

### Historical Analysis
1. **Backfill Trading Data**: Collect historical trades for known migrated tokens
2. **Feature Engineering**: Calculate all ML features
3. **Model Training**: Train on historical migration outcomes
4. **Validation**: Test on unseen data

## 🎯 Success Metrics

### Model Performance
- **Precision**: % of predicted migrations that actually occur
- **Recall**: % of actual migrations that were predicted
- **F1-Score**: Balanced measure of precision and recall
- **AUC-ROC**: Area under the receiver operating characteristic curve

### Business Impact
- **Early Detection**: Hours/days advance warning of migrations
- **Risk Reduction**: Avoid tokens with high rug risk
- **Opportunity Capture**: Identify tokens before major price moves
- **Portfolio Optimization**: Data-driven token selection

## 🔮 Future Enhancements

### Advanced Analytics
- **Social Sentiment**: Integrate Twitter/Telegram sentiment
- **On-chain Metrics**: Holder distribution, LP changes
- **Cross-token Patterns**: Creator behavior across multiple tokens
- **Market Conditions**: Macro market impact on migrations

### Real-time Features
- **Live Monitoring**: Real-time trade stream processing
- **Alert System**: Instant notifications for high-probability tokens
- **API Webhooks**: Integration with external systems
- **Dashboard**: Real-time visualization of migration candidates

## 📚 References

- [SolanaTracker API Documentation](https://docs.solanatracker.io/)
- [PostgreSQL JSONB Functions](https://www.postgresql.org/docs/current/functions-json.html)
- [Machine Learning for Time Series](https://scikit-learn.org/stable/modules/time_series.html)
- [Solana Program Library](https://spl.solana.com/)

---

**Next Steps**: Use the test script to validate the integration, then start collecting trading data for your token analysis pipeline.
