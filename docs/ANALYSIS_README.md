# 🚀 Comprehensive Solana Token Database Analysis

This analysis tool provides deep insights into ALL fields in your Solana token database with advanced machine learning and statistical techniques.

## 🎯 What This Analysis Covers

### 📊 Complete Field Analysis
- **All Database Columns**: Analyzes every single field in your tokens table
- **Raw Data JSONB**: Automatically parses and flattens the complex JSONB structure
- **Data Types**: Handles strings, numbers, dates, and nested JSON objects
- **Missing Data**: Identifies and quantifies missing values across all fields

### ⏰ Time-Based Analysis
- Daily, hourly, and weekly token creation patterns
- Peak activity periods and trends
- Cumulative growth analysis
- Recent activity metrics

### 👥 Creator Analysis
- Unique creator identification and statistics
- Creator productivity distribution
- Power creator analysis (top 1% creators)
- Creator concentration patterns (Pareto analysis)

### 🪙 Token Characteristics
- Analysis of all token-specific fields from raw_data
- Price, market cap, volume, and liquidity metrics (if available)
- Token metadata analysis
- Distribution patterns across all numerical fields

### 🧠 Advanced Machine Learning Analytics
- **Clustering Analysis**: Groups similar tokens using K-Means
- **Anomaly Detection**: Identifies unusual tokens using Isolation Forest
- **Correlation Analysis**: Finds relationships between numerical fields
- **Principal Component Analysis**: Dimensionality reduction insights

### 📈 Comprehensive Visualizations
- **Time Series Plots**: Token creation patterns over time
- **Creator Analysis Charts**: Top creators and distribution patterns
- **Distribution Histograms**: For all numerical fields
- **Correlation Heatmaps**: Relationships between variables
- **Text Analysis**: Word clouds and character frequency analysis

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
```bash
./run_analysis.sh
```

### Option 2: Manual Setup
```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run analysis
python3 analyze_database.py
```

## ⚙️ Configuration

Create a `.env` file with your database connection:

```env
DATABASE_URL=postgresql://postgres@localhost:5432/ses_db
```

Or customize for your setup:
```env
DATABASE_URL=postgresql://username:password@host:port/database_name
```

## 📊 Output Files

The analysis generates several visualization files:

1. **time_series_analysis.png** - Token creation patterns over time
2. **creator_analysis.png** - Creator productivity and distribution
3. **numerical_distributions.png** - Histograms of all numerical fields
4. **correlation_heatmap.png** - Correlation matrix of numerical variables
5. **text_analysis.png** - Text field analysis and patterns

## 🔍 Analysis Features

### Automatic Field Discovery
- Automatically discovers and analyzes ALL fields in your database
- Handles nested JSON structures in the raw_data field
- Adapts to your specific database schema

### Statistical Analysis
- Descriptive statistics for all numerical fields
- Outlier detection using IQR method
- Distribution analysis and normality testing
- Missing data patterns and recommendations

### Machine Learning Insights
- Unsupervised clustering to find token groups
- Anomaly detection for unusual tokens
- Feature importance analysis
- Dimensionality reduction for visualization

### Text Mining
- Token name and symbol analysis
- Character and word frequency analysis
- Common patterns identification
- Text length distribution analysis

## 📋 Requirements

- Python 3.8+
- PostgreSQL database with token data
- Required Python packages (see requirements.txt)

## 🛠️ Troubleshooting

### Database Connection Issues
- Verify your DATABASE_URL in the .env file
- Ensure PostgreSQL is running
- Check database permissions

### Missing Dependencies
```bash
pip install --upgrade pip setuptools wheel
pip install -r requirements.txt
```

### Memory Issues with Large Datasets
The analysis is optimized for large datasets, but for very large databases (>1M tokens), consider:
- Running analysis on a subset of data
- Increasing system memory
- Using database sampling techniques

## 📈 Understanding the Results

### Clustering Results
- Tokens are grouped into 5 clusters based on numerical characteristics
- Each cluster represents tokens with similar properties
- Use cluster analysis to identify token categories

### Anomaly Detection
- Identifies tokens that are significantly different from the norm
- Useful for finding unique or potentially problematic tokens
- Anomalies might indicate data quality issues or special token types

### Correlation Analysis
- Shows relationships between different token metrics
- High correlations (>0.7) indicate strong relationships
- Use for feature selection in predictive modeling

## 🎯 Next Steps

After running the analysis, you can:

1. **Investigate Anomalies**: Look into tokens flagged as anomalous
2. **Analyze Clusters**: Understand what makes each cluster unique
3. **Track Trends**: Monitor changes in token creation patterns
4. **Creator Insights**: Identify and analyze top-performing creators
5. **Data Quality**: Address missing data and outliers

## 🤝 Support

If you encounter issues or need customizations:
1. Check the console output for detailed error messages
2. Verify your database connection and data structure
3. Ensure all dependencies are properly installed
4. Review the generated log files for debugging information

---

**Happy Analyzing! 🚀📊**
