# 🚀 SES - Solana Enhanced Scanner

A comprehensive Solana token analysis and monitoring system with advanced ML capabilities for migration prediction and risk assessment.

## 🎯 Overview

SES combines high-performance Rust data collection with sophisticated Python ML analysis to provide real-time insights into Solana token markets, with a focus on predicting successful migrations from pump.fun to other DEXs.

## ✨ Key Features

- 🔄 **Real-time Data Collection**: High-performance Rust scanner using Helius API
- 🧠 **Advanced ML Analytics**: Migration prediction, anomaly detection, clustering
- 📊 **Comprehensive Analysis**: 50+ engineered features for token assessment
- 🎨 **Rich Visualizations**: Interactive charts and reports
- 🔍 **Risk Assessment**: Automated scoring and creator reputation analysis
- 📈 **Migration Tracking**: Pattern analysis across different DEX markets

## 📁 Project Structure

```
ses/
├── rust/                    # High-performance data collection
│   ├── src/                 # Rust source code
│   └── Cargo.toml          # Rust dependencies
├── python/                  # ML and analysis
│   ├── analysis/           # Core analysis modules
│   ├── ml/                 # Machine learning models
│   ├── scripts/            # Utility scripts
│   └── tests/              # Test suite
├── sql/                     # Database layer
│   ├── migrations/         # Database setup
│   ├── schemas/            # ML feature schemas
│   └── analysis/           # Analysis queries
├── outputs/                 # Generated content
│   ├── visualizations/     # Charts and graphs
│   ├── reports/            # Analysis reports
│   └── exports/            # Data exports
├── docs/                   # Documentation
├── config/                 # Configuration files
└── scripts/                # Build and utility scripts
```

## 🚀 Quick Start

### Prerequisites
- Rust 1.70+
- Python 3.9+
- PostgreSQL 14+
- Helius API key

### 1. Environment Setup
```bash
# Clone and setup
git clone <repository>
cd ses

# Copy environment template
cp .env.example .env
# Edit .env with your API keys and database URL
```

### 2. Database Setup
```bash
# Setup database
psql -f sql/migrations/setup_database.sql

# Create ML features schema
psql -f sql/schemas/ml_features_schema.sql
```

### 3. Install Dependencies
```bash
# Rust dependencies
cd rust && cargo build --release

# Python dependencies
pip install -r requirements.txt
```

### 4. Run Analysis
```bash
# Start data collection (Rust)
cd rust && cargo run

# Run ML analysis (Python)
./scripts/run_analysis.sh
```

## 📊 Analysis Capabilities

### 🧠 Machine Learning
- **Migration Prediction**: Predict which tokens will successfully migrate
- **Anomaly Detection**: Identify unusual or suspicious tokens
- **Clustering Analysis**: Group similar tokens for pattern recognition
- **Risk Scoring**: Automated risk assessment based on multiple factors

### 📈 Market Intelligence
- **Creator Analysis**: Track and score creator performance
- **Volume Prediction**: Forecast trading volume patterns
- **Social Media Impact**: Analyze correlation between social presence and success
- **Temporal Patterns**: Identify optimal timing for token launches

### 🎯 Key Metrics Tracked
- Migration success rates (currently 1.86% overall)
- Volume correlation (migrated tokens: 200x higher volume)
- Social media impact (full social presence: 40.18% migration rate)
- Creator success patterns (top creators: 36.4% success rate)

## 🛠️ Development

### Running Tests
```bash
# Rust tests
cd rust && cargo test

# Python tests
cd python && python -m pytest tests/
```

### Adding New Features
1. **Data Collection**: Add new fields in `rust/src/token_processor.rs`
2. **ML Features**: Update `sql/schemas/ml_features_schema.sql`
3. **Analysis**: Add new analysis in `python/analysis/`
4. **Visualization**: Update charts in analysis modules

## 📚 Documentation

- [Analysis Documentation](docs/ANALYSIS_README.md)
- [API Reference](docs/api.md)
- [Database Schema](docs/database.md)
- [ML Models](docs/ml_models.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details

## 🔗 Links

- [Helius API](https://helius.xyz/)
- [Solana Documentation](https://docs.solana.com/)
- [Pump.fun](https://pump.fun/)

---

**Built with ❤️ for the Solana ecosystem**
