# SES Backend

Un backend Rust all'avanguardia per la raccolta di dati sui token graduati di Solana tramite l'API di Solana Tracker.

## Caratteristiche

- **Framework moderno**: Utilizza Axum, un framework web ad alte prestazioni per Rust
- **Database PostgreSQL 17**: Storage ad alte prestazioni con JSONB per dati raw
- **Filtro pump.fun**: Salva solo token creati su https://pump.fun
- **API client robusto**: Client HTTP con timeout, retry e gestione errori
- **Storage PostgreSQL**: Database ad alte prestazioni come unico storage
- **Logging strutturato**: Sistema di logging completo con tracing
- **Health checks**: Endpoint per monitorare lo stato del servizio
- **Gestione errori**: Gestione completa degli errori con messaggi informativi
- **Configurazione flessibile**: Configurazione tramite variabili d'ambiente

## Endpoints

### POST /collect
Raccoglie i token graduati dall'API di Solana Tracker e li salva in un file JSON locale.

**Risposta di successo:**
```json
{
  "success": true,
  "message": "Successfully collected and saved 313 pump.fun tokens to database",
  "tokens_count": 384,
  "db_saved_count": 313,
  "timestamp": "2025-06-12T05:06:26.401974Z"
}
```

### GET /health
Verifica lo stato di salute del servizio e la connettività con l'API esterna.

### GET /status
Restituisce informazioni generali sul servizio e gli endpoint disponibili.

## Configurazione

Crea un file `.env` nella root del progetto:

```env
# Solana Tracker API Configuration
SOLANA_TRACKER_API_KEY=cfb8683b-785c-45d4-b3cd-8ab0b2b0c4d7
SOLANA_TRACKER_BASE_URL=https://data.solanatracker.io

# Server Configuration
SERVER_HOST=127.0.0.1
SERVER_PORT=3000

# Database Configuration
DATABASE_URL=postgresql://postgres@localhost:5432/ses_db
```

## Installazione e Avvio

1. **Clona il repository** (se necessario)

2. **Installa le dipendenze:**
   ```bash
   cargo build
   ```

3. **Configura le variabili d'ambiente:**
   - Copia il file `.env.example` in `.env`
   - Modifica l'API key se necessario

4. **Avvia il server:**
   ```bash
   cargo run
   ```

   Il server sarà disponibile su `http://127.0.0.1:3000`

## Utilizzo

### Raccogliere i token graduati
```bash
curl -X POST http://127.0.0.1:3000/collect
```

### Verificare lo stato del servizio
```bash
curl http://127.0.0.1:3000/health
```

### Ottenere informazioni sul servizio
```bash
curl http://127.0.0.1:3000/status
```

## Struttura del Progetto

```
src/
├── main.rs          # Entry point dell'applicazione
├── client.rs        # Client HTTP per l'API di Solana Tracker
├── database.rs      # Gestione database PostgreSQL e migrazioni
└── handlers.rs      # Handler per endpoint HTTP + strutture dati
```

## Gestione dei Dati

I dati vengono salvati direttamente nel database PostgreSQL con la seguente struttura:

### Tabella Tokens

```sql
CREATE TABLE tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(44) NOT NULL UNIQUE,
    creator_address VARCHAR(44),
    raw_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

Il campo `raw_data` contiene tutti i dati grezzi dell'API in formato JSONB per massima flessibilità e performance nelle query.

## Logging

Il sistema utilizza il crate `tracing` per logging strutturato. I livelli di log possono essere configurati tramite la variabile d'ambiente `RUST_LOG`:

```bash
RUST_LOG=ses=debug,tower_http=debug cargo run
```

## Dipendenze Principali

- **axum**: Framework web moderno e performante
- **reqwest**: Client HTTP con supporto async
- **serde**: Serializzazione/deserializzazione JSON
- **tokio**: Runtime asincrono
- **tracing**: Logging strutturato
- **anyhow**: Gestione errori semplificata

## Sviluppo

### Test
```bash
cargo test
```

### Formato del codice
```bash
cargo fmt
```

### Linting
```bash
cargo clippy
```

## API di Solana Tracker

Questo backend utilizza l'endpoint `/tokens/multi/graduated` dell'API di Solana Tracker per ottenere informazioni sui token che hanno completato il processo di graduazione su piattaforme come Pump.fun.

Documentazione API: https://docs.solanatracker.io/public-data-api/docs

## Licenza

[Inserire informazioni sulla licenza]
