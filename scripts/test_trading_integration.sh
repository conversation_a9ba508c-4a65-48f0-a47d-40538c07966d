#!/bin/bash

# 🚀 Test script for SolanaTracker Trading Data Integration
# This script tests the complete workflow of collecting and analyzing trading data

set -e

echo "🚀 Testing SolanaTracker Trading Data Integration"
echo "================================================"

# Configuration
SERVER_URL="http://127.0.0.1:3000"
TEST_TOKEN="CgDencV766uQUArDFp1GtUjubCcQYreRqBm5xmyEpump"  # Example token from the API call
API_KEY="cfb8683b-785c-45d4-b3cd-8ab0b2b0c4d7"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if server is running
check_server() {
    print_status "Checking if server is running..."
    if curl -s "$SERVER_URL/health" > /dev/null; then
        print_success "Server is running"
        return 0
    else
        print_error "Server is not running. Please start the server first."
        return 1
    fi
}

# Function to test health check
test_health_check() {
    print_status "Testing health check endpoint..."
    
    response=$(curl -s "$SERVER_URL/health")
    if echo "$response" | jq -e '.status == "healthy"' > /dev/null 2>&1; then
        print_success "Health check passed"
        return 0
    else
        print_warning "Health check failed or returned unexpected response"
        echo "Response: $response"
        return 1
    fi
}

# Function to test direct API call
test_direct_api() {
    print_status "Testing direct SolanaTracker API call..."
    
    api_response=$(curl -s -X GET \
        "https://data.solanatracker.io/trades/$TEST_TOKEN?limit=5&sortDirection=asc" \
        -H "x-api-key: $API_KEY" \
        -H "Content-Type: application/json")
    
    if echo "$api_response" | jq -e '.trades | length > 0' > /dev/null 2>&1; then
        trades_count=$(echo "$api_response" | jq '.trades | length')
        print_success "Direct API call successful - found $trades_count trades"
        
        # Show sample trade data
        print_status "Sample trade data:"
        echo "$api_response" | jq '.trades[0]' | head -10
        return 0
    else
        print_error "Direct API call failed or returned no trades"
        echo "Response: $api_response"
        return 1
    fi
}

# Function to test trades collection endpoint
test_trades_collection() {
    print_status "Testing trades collection endpoint..."
    
    response=$(curl -s "$SERVER_URL/trades?token_address=$TEST_TOKEN&limit=10&update_features=true")
    
    if echo "$response" | jq -e '.success == true' > /dev/null 2>&1; then
        trades_fetched=$(echo "$response" | jq -r '.trades_fetched')
        trades_stored=$(echo "$response" | jq -r '.trades_stored')
        print_success "Trades collection successful - fetched: $trades_fetched, stored: $trades_stored"
        
        # Check if migration analysis is available
        if echo "$response" | jq -e '.migration_analysis' > /dev/null 2>&1; then
            migration_prob=$(echo "$response" | jq -r '.migration_analysis.migration_probability')
            recommendation=$(echo "$response" | jq -r '.migration_analysis.recommendation')
            print_success "Migration analysis: probability=${migration_prob}, recommendation=${recommendation}"
        else
            print_warning "No migration analysis in response"
        fi
        
        return 0
    else
        print_error "Trades collection failed"
        echo "Response: $response"
        return 1
    fi
}

# Function to test database schema
test_database_schema() {
    print_status "Testing database schema (requires psql)..."
    
    if ! command -v psql &> /dev/null; then
        print_warning "psql not found, skipping database schema test"
        return 0
    fi
    
    if [ -z "$DATABASE_URL" ]; then
        print_warning "DATABASE_URL not set, skipping database schema test"
        return 0
    fi
    
    # Test if tables exist
    tables_query="SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('token_trades', 'token_migration_features');"
    
    tables_result=$(psql "$DATABASE_URL" -t -c "$tables_query" 2>/dev/null || echo "")
    
    if echo "$tables_result" | grep -q "token_trades"; then
        print_success "token_trades table exists"
    else
        print_warning "token_trades table not found"
    fi
    
    if echo "$tables_result" | grep -q "token_migration_features"; then
        print_success "token_migration_features table exists"
    else
        print_warning "token_migration_features table not found"
    fi
}

# Function to run SQL schema setup
setup_database_schema() {
    print_status "Setting up database schema..."
    
    if [ -z "$DATABASE_URL" ]; then
        print_warning "DATABASE_URL not set, skipping schema setup"
        return 0
    fi
    
    if ! command -v psql &> /dev/null; then
        print_warning "psql not found, skipping schema setup"
        return 0
    fi
    
    # Run the ML features schema
    if [ -f "sql/schemas/ml_features_schema.sql" ]; then
        print_status "Applying ML features schema..."
        if psql "$DATABASE_URL" -f "sql/schemas/ml_features_schema.sql" > /dev/null 2>&1; then
            print_success "ML features schema applied successfully"
        else
            print_error "Failed to apply ML features schema"
            return 1
        fi
    else
        print_warning "ML features schema file not found"
    fi
    
    # Run the integration functions
    if [ -f "sql/functions/solanatracker_integration.sql" ]; then
        print_status "Applying SolanaTracker integration functions..."
        if psql "$DATABASE_URL" -f "sql/functions/solanatracker_integration.sql" > /dev/null 2>&1; then
            print_success "Integration functions applied successfully"
        else
            print_error "Failed to apply integration functions"
            return 1
        fi
    else
        print_warning "Integration functions file not found"
    fi
}

# Main test execution
main() {
    echo
    print_status "Starting comprehensive trading integration test..."
    echo
    
    # Check if we should setup database schema
    if [ "$1" = "--setup-db" ]; then
        setup_database_schema
        echo
    fi
    
    # Run tests
    test_count=0
    passed_count=0
    
    # Test 1: Server health
    ((test_count++))
    if check_server && test_health_check; then
        ((passed_count++))
    fi
    echo
    
    # Test 2: Direct API access
    ((test_count++))
    if test_direct_api; then
        ((passed_count++))
    fi
    echo
    
    # Test 3: Database schema
    ((test_count++))
    if test_database_schema; then
        ((passed_count++))
    fi
    echo
    
    # Test 4: Trades collection endpoint
    ((test_count++))
    if test_trades_collection; then
        ((passed_count++))
    fi
    echo
    
    # Summary
    echo "================================================"
    print_status "Test Summary: $passed_count/$test_count tests passed"
    
    if [ $passed_count -eq $test_count ]; then
        print_success "All tests passed! 🎉"
        echo
        print_status "Next steps:"
        echo "1. Use the /trades endpoint to collect trading data for tokens"
        echo "2. Analyze migration patterns using the ML features"
        echo "3. Build predictive models based on the collected data"
        return 0
    else
        print_error "Some tests failed. Please check the output above."
        return 1
    fi
}

# Run main function with all arguments
main "$@"
