#!/bin/bash

# 🚀 Solana Token Database Analysis Runner
# This script sets up the Python environment and runs the comprehensive analysis

echo "🚀 Setting up Solana Token Database Analysis"
echo "============================================"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv venv
    if [ $? -ne 0 ]; then
        echo "❌ Failed to create virtual environment"
        exit 1
    fi
    echo "✅ Virtual environment created"
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip

# Install requirements
echo "📥 Installing Python dependencies..."
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    echo "💡 Try running: pip install --upgrade pip setuptools wheel"
    exit 1
fi

echo "✅ All dependencies installed successfully"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  No .env file found. Creating template..."
    cat > .env << EOF
# Database Configuration
DATABASE_URL=postgresql://postgres@localhost:5432/ses_db

# Optional: Customize database connection
# DATABASE_URL=postgresql://username:password@host:port/database_name
EOF
    echo "📝 Created .env template. Please update DATABASE_URL if needed."
fi

# Run the analysis
echo ""
echo "🔍 Starting comprehensive database analysis..."
echo "============================================"

python3 python/analysis/analyze_database.py

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Analysis completed successfully!"
    echo "📊 Check the generated visualization files:"
    echo "   - outputs/visualizations/time_series_analysis.png"
    echo "   - outputs/visualizations/creator_analysis.png"
    echo "   - outputs/visualizations/numerical_distributions.png"
    echo "   - outputs/visualizations/correlation_heatmap.png"
    echo "   - outputs/visualizations/text_analysis.png"
else
    echo "❌ Analysis failed. Check the error messages above."
    exit 1
fi

echo ""
echo "✅ All done! Check the console output and generated PNG files."
