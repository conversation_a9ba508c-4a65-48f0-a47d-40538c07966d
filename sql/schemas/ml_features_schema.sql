-- 🚀 ML Features Schema for Solana Token Migration Prediction
-- Predicts which tokens will successfully migrate from pump.fun to other DEXs
-- Focus: Early detection system for token migration opportunities

-- Drop existing objects if they exist
DROP TRIGGER IF EXISTS token_migration_trigger ON tokens;
DROP FUNCTION IF EXISTS update_migration_features();
DROP FUNCTION IF EXISTS calculate_creator_stats();
DROP FUNCTION IF EXISTS calculate_trading_features();
DROP TABLE IF EXISTS token_migration_features CASCADE;
DROP TABLE IF EXISTS token_trades CASCADE;
DROP TABLE IF EXISTS tokens CASCADE;
DROP TYPE IF EXISTS creator_stats_type CASCADE;

-- Create the base tokens table first
CREATE TABLE tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(44) NOT NULL UNIQUE,
    creator_address VARCHAR(44), -- Made optional to handle tokens without creator
    raw_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for tokens table
CREATE INDEX idx_tokens_token_address ON tokens(token_address);
CREATE INDEX idx_tokens_creator_address ON tokens(creator_address);
CREATE INDEX idx_tokens_created_at ON tokens(created_at);
CREATE INDEX idx_tokens_raw_data_gin ON tokens USING GIN(raw_data);

-- Create custom type for creator stats
CREATE TYPE creator_stats_type AS (
    success_rate DECIMAL(5,4),
    total_tokens INTEGER,
    avg_volume DECIMAL(20,2),
    reputation_score INTEGER
);

-- 📊 TRADES TABLE - Store individual trading transactions
-- This table captures detailed trading data from SolanaTracker API
CREATE TABLE token_trades (
    -- Primary identifiers
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(44) NOT NULL,
    tx_hash VARCHAR(88) NOT NULL UNIQUE, -- Transaction hash
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Trade details
    trade_time TIMESTAMP WITH TIME ZONE NOT NULL, -- Actual trade timestamp
    trade_type VARCHAR(10) NOT NULL CHECK (trade_type IN ('buy', 'sell')),
    wallet_address VARCHAR(44) NOT NULL,

    -- Trade amounts and pricing
    token_amount DECIMAL(30,6) NOT NULL, -- Amount of tokens traded
    price_usd DECIMAL(20,10) NOT NULL, -- Price per token in USD
    volume_usd DECIMAL(20,6) NOT NULL, -- Total volume in USD
    volume_sol DECIMAL(20,9), -- Total volume in SOL

    -- Platform and pool information
    program VARCHAR(50), -- Trading program (e.g., "pump")
    pools JSONB, -- Array of pool addresses involved

    -- Derived fields for analysis
    trade_size_category VARCHAR(20), -- 'micro', 'small', 'medium', 'large', 'whale'
    is_first_buy BOOLEAN DEFAULT FALSE, -- First buy for this wallet
    is_final_sell BOOLEAN DEFAULT FALSE, -- Final sell for this wallet

    -- Metadata
    raw_data JSONB, -- Store complete API response for future analysis
    data_source VARCHAR(50) DEFAULT 'solanatracker'
);

-- Create indexes for trades table
CREATE INDEX idx_token_trades_token_address ON token_trades(token_address);
CREATE INDEX idx_token_trades_trade_time ON token_trades(trade_time);
CREATE INDEX idx_token_trades_wallet ON token_trades(wallet_address);
CREATE INDEX idx_token_trades_type ON token_trades(trade_type);
CREATE INDEX idx_token_trades_volume ON token_trades(volume_usd);
CREATE INDEX idx_token_trades_tx_hash ON token_trades(tx_hash);

-- Composite indexes for analytics
CREATE INDEX idx_token_trades_analytics ON token_trades(
    token_address, trade_time, trade_type, volume_usd
);

CREATE INDEX idx_token_trades_wallet_activity ON token_trades(
    wallet_address, trade_time, trade_type
);

-- Create the features table for migration prediction
CREATE TABLE token_migration_features (
    -- Primary identifiers
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(44) NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Basic token info
    creator_address VARCHAR(44),
    token_name VARCHAR(100),
    token_symbol VARCHAR(20),
    token_decimals INTEGER,
    
    -- Market data features (snapshot al momento della raccolta)
    current_price_usd DECIMAL(20,10),
    market_cap_usd DECIMAL(20,2),
    liquidity_usd DECIMAL(20,2),
    volume_total DECIMAL(20,2), -- volume totale dalla creazione

    -- Trading activity features (basic)
    total_transactions INTEGER DEFAULT 0,
    buy_transactions INTEGER DEFAULT 0,
    sell_transactions INTEGER DEFAULT 0,
    unique_holders INTEGER DEFAULT 0,

    -- 🔥 ADVANCED TRADING FEATURES (from detailed trades data)
    -- Volume and transaction patterns
    total_trade_volume_usd DECIMAL(20,2) DEFAULT 0, -- Sum of all trade volumes
    avg_trade_size_usd DECIMAL(20,6) DEFAULT 0, -- Average trade size
    median_trade_size_usd DECIMAL(20,6) DEFAULT 0, -- Median trade size
    max_single_trade_usd DECIMAL(20,6) DEFAULT 0, -- Largest single trade

    -- Buy/Sell dynamics
    buy_sell_ratio DECIMAL(10,4) DEFAULT 0, -- Ratio of buy to sell transactions
    buy_volume_ratio DECIMAL(10,4) DEFAULT 0, -- Ratio of buy to total volume
    net_volume_flow DECIMAL(20,2) DEFAULT 0, -- Buy volume - Sell volume

    -- Wallet behavior patterns
    unique_buyers INTEGER DEFAULT 0, -- Number of unique buying wallets
    unique_sellers INTEGER DEFAULT 0, -- Number of unique selling wallets
    whale_trades_count INTEGER DEFAULT 0, -- Trades > $10k
    retail_trades_count INTEGER DEFAULT 0, -- Trades < $100

    -- Time-based trading patterns
    trades_last_1h INTEGER DEFAULT 0,
    trades_last_6h INTEGER DEFAULT 0,
    trades_last_24h INTEGER DEFAULT 0,
    volume_last_1h DECIMAL(20,2) DEFAULT 0,
    volume_last_6h DECIMAL(20,2) DEFAULT 0,
    volume_last_24h DECIMAL(20,2) DEFAULT 0,

    -- Trading velocity and momentum
    trading_velocity DECIMAL(10,4) DEFAULT 0, -- Trades per hour since creation
    volume_velocity DECIMAL(20,6) DEFAULT 0, -- Volume per hour since creation
    momentum_score DECIMAL(10,4) DEFAULT 0, -- Recent activity vs historical average

    -- Market maker vs retail patterns
    suspected_mm_trades INTEGER DEFAULT 0, -- Suspected market maker trades
    retail_participation_ratio DECIMAL(5,4) DEFAULT 0, -- % of trades that are retail

    -- Price impact and slippage indicators
    avg_price_impact DECIMAL(10,6) DEFAULT 0, -- Average price change per trade
    high_impact_trades INTEGER DEFAULT 0, -- Trades causing >5% price change

    -- 💎 HOLDER ANALYSIS FEATURES (derived from trades)
    -- Holder behavior patterns
    total_unique_holders INTEGER DEFAULT 0, -- Unique wallets (buyers + sellers)
    holder_turnover_rate DECIMAL(5,4) DEFAULT 0, -- Sellers / Total holders
    avg_hold_time_hours DECIMAL(10,2) DEFAULT 0, -- Average time between buy and sell
    holder_loyalty_score DECIMAL(5,4) DEFAULT 0, -- % holders that haven't sold

    -- New vs churned holders
    new_holders_24h INTEGER DEFAULT 0, -- New holders in last 24h
    churned_holders_24h INTEGER DEFAULT 0, -- Holders who sold everything in 24h

    -- Holder concentration and distribution
    holder_concentration_top10 DECIMAL(5,4) DEFAULT 0, -- % volume from top 10 holders
    diamond_hands_ratio DECIMAL(5,4) DEFAULT 0, -- % holders with only buys
    paper_hands_ratio DECIMAL(5,4) DEFAULT 0, -- % holders who sold within 1h

    -- Holder categories
    whale_holder_count INTEGER DEFAULT 0, -- Holders with >$10k volume
    retail_holder_count INTEGER DEFAULT 0, -- Holders with <$100 volume
    swing_trader_count INTEGER DEFAULT 0, -- Holders with multiple buy/sell cycles

    -- Entry/exit timing patterns
    early_adopter_ratio DECIMAL(5,4) DEFAULT 0, -- % holders who entered in first 10% of token life
    fomo_entry_ratio DECIMAL(5,4) DEFAULT 0, -- % holders who entered during high momentum
    panic_exit_ratio DECIMAL(5,4) DEFAULT 0, -- % holders who sold during price drops

    -- Holder wealth distribution
    holder_gini_coefficient DECIMAL(5,4) DEFAULT 0, -- Wealth concentration (0=equal, 1=concentrated)
    median_holder_value_usd DECIMAL(20,6) DEFAULT 0, -- Median holding value
    top_holder_dominance DECIMAL(5,4) DEFAULT 0, -- % owned by largest holder

    -- Bonding curve features (chiave per migrazione)
    curve_percentage DECIMAL(5,2), -- % completamento bonding curve
    has_curve BOOLEAN DEFAULT TRUE, -- se ha ancora bonding curve attiva
    curve_address VARCHAR(44), -- indirizzo della bonding curve
    
    -- Price change features (for trend analysis)
    price_change_1m DECIMAL(10,4),
    price_change_5m DECIMAL(10,4),
    price_change_15m DECIMAL(10,4),
    price_change_30m DECIMAL(10,4),
    price_change_1h DECIMAL(10,4),
    price_change_2h DECIMAL(10,4),
    price_change_6h DECIMAL(10,4),
    price_change_12h DECIMAL(10,4),
    price_change_24h DECIMAL(10,4),
    
    -- Risk assessment features
    risk_score INTEGER DEFAULT 0,
    is_rugged BOOLEAN DEFAULT FALSE,
    has_social_media BOOLEAN DEFAULT FALSE,
    bonding_curve_complete BOOLEAN DEFAULT FALSE,
    
    -- Social media presence (boolean features)
    has_twitter BOOLEAN DEFAULT FALSE,
    has_telegram BOOLEAN DEFAULT FALSE,
    has_website BOOLEAN DEFAULT FALSE,
    has_discord BOOLEAN DEFAULT FALSE,
    has_instagram BOOLEAN DEFAULT FALSE,
    has_youtube BOOLEAN DEFAULT FALSE,
    has_reddit BOOLEAN DEFAULT FALSE,
    has_github BOOLEAN DEFAULT FALSE,
    
    -- Platform features
    created_on_platform VARCHAR(50),
    market_type VARCHAR(50),
    lp_burn_percentage DECIMAL(5,2) DEFAULT 0, -- % LP bruciato
    
    -- Derived features for ML
    volume_to_mcap_ratio DECIMAL(10,6),
    holder_concentration DECIMAL(10,6), -- top holder percentage
    social_score INTEGER DEFAULT 0, -- count of social media platforms
    volatility_score DECIMAL(10,4), -- based on price changes
    
    -- Time-based features
    token_age_hours INTEGER, -- hours since creation
    last_activity_hours INTEGER, -- hours since last transaction
    
    -- Categorical features (encoded)
    risk_level_encoded INTEGER DEFAULT 0, -- 0=low, 1=medium, 2=high
    market_tier INTEGER DEFAULT 0, -- 0=micro, 1=small, 2=medium, 3=large
    
    -- CREATOR HISTORICAL FEATURES (Sistema 1)
    creator_success_rate DECIMAL(5,4), -- % of creator's tokens that migrated
    creator_total_tokens INTEGER DEFAULT 0, -- total tokens created by this creator
    creator_avg_migration_time INTEGER, -- average hours to migration for creator
    creator_avg_volume_at_migration DECIMAL(20,2), -- average volume when creator's tokens migrate
    creator_reputation_score INTEGER DEFAULT 0, -- 0-100 based on historical success

    -- MIGRATION PREDICTION TARGETS (Labels for ML)
    did_migrate BOOLEAN DEFAULT FALSE, -- TRUE if token migrated to other DEX
    migration_type VARCHAR(20), -- 'pumpfun-amm', 'raydium', 'meteora-dyn', etc.
    migration_success_level INTEGER DEFAULT 0, -- 0=no migration, 1=low volume, 2=medium, 3=high volume
    estimated_hours_to_migration INTEGER, -- stima ore dalla creazione alla migrazione
    volume_at_snapshot DECIMAL(20,2), -- volume al momento dello snapshot

    -- SUCCESS INDICATORS (per calcolare le label)
    final_market_type VARCHAR(20), -- tipo di mercato finale
    has_bundle_id BOOLEAN DEFAULT FALSE, -- se ha bundle ID (indica migrazione)
    
    -- Metadata
    feature_version INTEGER DEFAULT 1,
    last_ml_update TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX idx_migration_features_token_address ON token_migration_features(token_address);
CREATE INDEX idx_migration_features_creator ON token_migration_features(creator_address);
CREATE INDEX idx_migration_features_market_cap ON token_migration_features(market_cap_usd);
CREATE INDEX idx_migration_features_volume ON token_migration_features(volume_total);
CREATE INDEX idx_migration_features_risk ON token_migration_features(risk_score);
CREATE INDEX idx_migration_features_created_at ON token_migration_features(created_at);
CREATE INDEX idx_migration_features_migration ON token_migration_features(did_migrate);
CREATE INDEX idx_migration_features_creator_success ON token_migration_features(creator_success_rate);

-- Composite indexes for ML queries
CREATE INDEX idx_migration_features_ml_training ON token_migration_features(
    market_cap_usd, volume_total, risk_score, social_score, creator_success_rate
) WHERE did_migrate IS NOT NULL;

CREATE INDEX idx_migration_features_migrated_tokens ON token_migration_features(
    created_at, migration_type, volume_total
) WHERE did_migrate = TRUE;

CREATE INDEX idx_migration_features_curve_completion ON token_migration_features(
    curve_percentage, has_curve, liquidity_usd
) WHERE curve_percentage > 90;

-- Function to calculate creator historical statistics
CREATE OR REPLACE FUNCTION calculate_creator_stats(creator_addr VARCHAR(44))
RETURNS TABLE(
    success_rate DECIMAL(5,4),
    total_tokens INTEGER,
    avg_volume DECIMAL(20,2),
    reputation_score INTEGER
) AS $$
DECLARE
    total_count INTEGER := 0;
    migrated_count INTEGER := 0;
    avg_vol DECIMAL(20,2) := 0;
    reputation INTEGER := 0;
BEGIN
    -- Count total tokens by creator
    SELECT COUNT(*) INTO total_count
    FROM tokens
    WHERE creator_address = creator_addr;

    -- Count migrated tokens by creator
    SELECT COUNT(*) INTO migrated_count
    FROM tokens
    WHERE creator_address = creator_addr
    AND (raw_data->'pools'->0->>'market') IN ('pumpfun-amm', 'raydium', 'meteora-dyn', 'meteora-dyn-v2', 'raydium-cpmm');

    -- Calculate average volume for migrated tokens
    SELECT COALESCE(AVG((raw_data->'pools'->0->'txns'->'volume')::DECIMAL), 0) INTO avg_vol
    FROM tokens
    WHERE creator_address = creator_addr
    AND (raw_data->'pools'->0->>'market') IN ('pumpfun-amm', 'raydium', 'meteora-dyn', 'meteora-dyn-v2', 'raydium-cpmm');

    -- Calculate reputation score (0-100)
    IF total_count > 0 THEN
        reputation := LEAST(100,
            (migrated_count * 50 / total_count) + -- 50% weight on success rate
            (CASE WHEN total_count >= 10 THEN 25 ELSE total_count * 2.5 END) + -- 25% weight on experience
            (CASE WHEN avg_vol > 100000 THEN 25 ELSE LEAST(25, avg_vol / 4000) END) -- 25% weight on volume
        );
    END IF;

    RETURN QUERY SELECT
        CASE WHEN total_count > 0 THEN migrated_count::DECIMAL / total_count ELSE 0 END,
        total_count,
        avg_vol,
        reputation;
END;
$$ LANGUAGE plpgsql;

-- 📈 Function to calculate advanced trading features from trades data
CREATE OR REPLACE FUNCTION calculate_trading_features(p_token_address VARCHAR(44))
RETURNS TABLE(
    total_trade_volume_usd DECIMAL(20,2),
    avg_trade_size_usd DECIMAL(20,6),
    median_trade_size_usd DECIMAL(20,6),
    max_single_trade_usd DECIMAL(20,6),
    buy_sell_ratio DECIMAL(10,4),
    buy_volume_ratio DECIMAL(10,4),
    net_volume_flow DECIMAL(20,2),
    unique_buyers INTEGER,
    unique_sellers INTEGER,
    whale_trades_count INTEGER,
    retail_trades_count INTEGER,
    trades_last_1h INTEGER,
    trades_last_6h INTEGER,
    trades_last_24h INTEGER,
    volume_last_1h DECIMAL(20,2),
    volume_last_6h DECIMAL(20,2),
    volume_last_24h DECIMAL(20,2),
    trading_velocity DECIMAL(10,4),
    volume_velocity DECIMAL(20,6),
    momentum_score DECIMAL(10,4),
    suspected_mm_trades INTEGER,
    retail_participation_ratio DECIMAL(5,4),
    avg_price_impact DECIMAL(10,6),
    high_impact_trades INTEGER
) AS $$
DECLARE
    total_vol DECIMAL(20,2) := 0;
    avg_size DECIMAL(20,6) := 0;
    median_size DECIMAL(20,6) := 0;
    max_size DECIMAL(20,6) := 0;
    buy_sell_r DECIMAL(10,4) := 0;
    buy_vol_r DECIMAL(10,4) := 0;
    net_flow DECIMAL(20,2) := 0;
    buyers_count INTEGER := 0;
    sellers_count INTEGER := 0;
    whale_count INTEGER := 0;
    retail_count INTEGER := 0;
    trades_1h INTEGER := 0;
    trades_6h INTEGER := 0;
    trades_24h INTEGER := 0;
    vol_1h DECIMAL(20,2) := 0;
    vol_6h DECIMAL(20,2) := 0;
    vol_24h DECIMAL(20,2) := 0;
    trade_vel DECIMAL(10,4) := 0;
    vol_vel DECIMAL(20,6) := 0;
    momentum DECIMAL(10,4) := 0;
    mm_trades INTEGER := 0;
    retail_ratio DECIMAL(5,4) := 0;
    price_impact DECIMAL(10,6) := 0;
    high_impact INTEGER := 0;
    total_trades INTEGER := 0;
    buy_trades INTEGER := 0;
    sell_trades INTEGER := 0;
    buy_volume DECIMAL(20,2) := 0;
    sell_volume DECIMAL(20,2) := 0;
    token_age_hours DECIMAL := 0;
BEGIN
    -- Get basic trade statistics
    SELECT
        COALESCE(SUM(volume_usd), 0),
        COALESCE(AVG(volume_usd), 0),
        COALESCE(MAX(volume_usd), 0),
        COUNT(*),
        COUNT(CASE WHEN trade_type = 'buy' THEN 1 END),
        COUNT(CASE WHEN trade_type = 'sell' THEN 1 END),
        COALESCE(SUM(CASE WHEN trade_type = 'buy' THEN volume_usd ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN trade_type = 'sell' THEN volume_usd ELSE 0 END), 0)
    INTO total_vol, avg_size, max_size, total_trades, buy_trades, sell_trades, buy_volume, sell_volume
    FROM token_trades
    WHERE token_address = p_token_address;

    -- Calculate median trade size
    SELECT COALESCE(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY volume_usd), 0)
    INTO median_size
    FROM token_trades
    WHERE token_address = p_token_address;

    -- Calculate ratios
    IF sell_trades > 0 THEN
        buy_sell_r := buy_trades::DECIMAL / sell_trades;
    END IF;

    IF total_vol > 0 THEN
        buy_vol_r := buy_volume / total_vol;
    END IF;

    net_flow := buy_volume - sell_volume;

    -- Count unique buyers and sellers
    SELECT
        COUNT(DISTINCT CASE WHEN trade_type = 'buy' THEN wallet_address END),
        COUNT(DISTINCT CASE WHEN trade_type = 'sell' THEN wallet_address END)
    INTO buyers_count, sellers_count
    FROM token_trades
    WHERE token_address = p_token_address;

    -- Count whale and retail trades
    SELECT
        COUNT(CASE WHEN volume_usd >= 10000 THEN 1 END),
        COUNT(CASE WHEN volume_usd < 100 THEN 1 END)
    INTO whale_count, retail_count
    FROM token_trades
    WHERE token_address = p_token_address;

    -- Time-based metrics
    SELECT
        COUNT(CASE WHEN trade_time >= NOW() - INTERVAL '1 hour' THEN 1 END),
        COUNT(CASE WHEN trade_time >= NOW() - INTERVAL '6 hours' THEN 1 END),
        COUNT(CASE WHEN trade_time >= NOW() - INTERVAL '24 hours' THEN 1 END),
        COALESCE(SUM(CASE WHEN trade_time >= NOW() - INTERVAL '1 hour' THEN volume_usd ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN trade_time >= NOW() - INTERVAL '6 hours' THEN volume_usd ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN trade_time >= NOW() - INTERVAL '24 hours' THEN volume_usd ELSE 0 END), 0)
    INTO trades_1h, trades_6h, trades_24h, vol_1h, vol_6h, vol_24h
    FROM token_trades
    WHERE token_address = p_token_address;

    -- Calculate trading velocity (need token creation time)
    SELECT EXTRACT(EPOCH FROM (NOW() - MIN(trade_time))) / 3600
    INTO token_age_hours
    FROM token_trades
    WHERE token_address = p_token_address;

    IF token_age_hours > 0 THEN
        trade_vel := total_trades / token_age_hours;
        vol_vel := total_vol / token_age_hours;
    END IF;

    -- Calculate momentum (recent vs historical average)
    IF token_age_hours > 24 THEN
        momentum := (vol_24h / 24.0) / (total_vol / token_age_hours);
    ELSE
        momentum := 1.0; -- Default for new tokens
    END IF;

    -- Detect suspected market maker trades (rapid buy-sell pairs)
    SELECT COUNT(*)
    INTO mm_trades
    FROM token_trades t1
    JOIN token_trades t2 ON t1.wallet_address = t2.wallet_address
    WHERE t1.token_address = p_token_address
    AND t2.token_address = p_token_address
    AND t1.trade_type = 'buy'
    AND t2.trade_type = 'sell'
    AND t2.trade_time BETWEEN t1.trade_time AND t1.trade_time + INTERVAL '5 minutes'
    AND ABS(t1.volume_usd - t2.volume_usd) / t1.volume_usd < 0.05; -- Similar volumes

    -- Calculate retail participation ratio
    IF total_trades > 0 THEN
        retail_ratio := retail_count::DECIMAL / total_trades;
    END IF;

    -- Calculate average price impact (simplified)
    -- This would need price data before/after each trade for accurate calculation
    price_impact := 0; -- Placeholder
    high_impact := 0; -- Placeholder

    RETURN QUERY SELECT
        total_vol, avg_size, median_size, max_size,
        buy_sell_r, buy_vol_r, net_flow,
        buyers_count, sellers_count, whale_count, retail_count,
        trades_1h, trades_6h, trades_24h,
        vol_1h, vol_6h, vol_24h,
        trade_vel, vol_vel, momentum,
        mm_trades, retail_ratio, price_impact, high_impact;
END;
$$ LANGUAGE plpgsql;

-- 💎 Function to calculate holder analysis features from trades data
CREATE OR REPLACE FUNCTION calculate_holder_features(p_token_address VARCHAR(44))
RETURNS TABLE(
    total_unique_holders INTEGER,
    holder_turnover_rate DECIMAL(5,4),
    avg_hold_time_hours DECIMAL(10,2),
    holder_loyalty_score DECIMAL(5,4),
    new_holders_24h INTEGER,
    churned_holders_24h INTEGER,
    holder_concentration_top10 DECIMAL(5,4),
    diamond_hands_ratio DECIMAL(5,4),
    paper_hands_ratio DECIMAL(5,4),
    whale_holder_count INTEGER,
    retail_holder_count INTEGER,
    swing_trader_count INTEGER,
    early_adopter_ratio DECIMAL(5,4),
    fomo_entry_ratio DECIMAL(5,4),
    panic_exit_ratio DECIMAL(5,4),
    holder_gini_coefficient DECIMAL(5,4),
    median_holder_value_usd DECIMAL(20,6),
    top_holder_dominance DECIMAL(5,4)
) AS $$
DECLARE
    total_holders INTEGER := 0;
    turnover_rate DECIMAL(5,4) := 0;
    avg_hold_time DECIMAL(10,2) := 0;
    loyalty_score DECIMAL(5,4) := 0;
    new_24h INTEGER := 0;
    churned_24h INTEGER := 0;
    concentration_top10 DECIMAL(5,4) := 0;
    diamond_hands DECIMAL(5,4) := 0;
    paper_hands DECIMAL(5,4) := 0;
    whale_holders INTEGER := 0;
    retail_holders INTEGER := 0;
    swing_traders INTEGER := 0;
    early_adopters DECIMAL(5,4) := 0;
    fomo_entries DECIMAL(5,4) := 0;
    panic_exits DECIMAL(5,4) := 0;
    gini_coeff DECIMAL(5,4) := 0;
    median_value DECIMAL(20,6) := 0;
    top_dominance DECIMAL(5,4) := 0;

    -- Helper variables
    total_buyers INTEGER := 0;
    total_sellers INTEGER := 0;
    token_start_time TIMESTAMP;
    token_age_hours DECIMAL := 0;
BEGIN
    -- Get token start time and age
    SELECT MIN(trade_time) INTO token_start_time
    FROM token_trades
    WHERE token_address = p_token_address;

    IF token_start_time IS NOT NULL THEN
        token_age_hours := EXTRACT(EPOCH FROM (NOW() - token_start_time)) / 3600;
    END IF;

    -- Calculate total unique holders (buyers + sellers, but count each wallet once)
    SELECT COUNT(DISTINCT wallet_address) INTO total_holders
    FROM token_trades
    WHERE token_address = p_token_address;

    -- Count buyers and sellers separately
    SELECT
        COUNT(DISTINCT CASE WHEN trade_type = 'buy' THEN wallet_address END),
        COUNT(DISTINCT CASE WHEN trade_type = 'sell' THEN wallet_address END)
    INTO total_buyers, total_sellers
    FROM token_trades
    WHERE token_address = p_token_address;

    -- Calculate turnover rate (sellers / total holders)
    IF total_holders > 0 THEN
        turnover_rate := total_sellers::DECIMAL / total_holders;
    END IF;

    -- Calculate average hold time (simplified: time between first buy and last sell per wallet)
    WITH wallet_hold_times AS (
        SELECT
            wallet_address,
            MIN(CASE WHEN trade_type = 'buy' THEN trade_time END) as first_buy,
            MAX(CASE WHEN trade_type = 'sell' THEN trade_time END) as last_sell
        FROM token_trades
        WHERE token_address = p_token_address
        GROUP BY wallet_address
        HAVING MIN(CASE WHEN trade_type = 'buy' THEN trade_time END) IS NOT NULL
        AND MAX(CASE WHEN trade_type = 'sell' THEN trade_time END) IS NOT NULL
    )
    SELECT COALESCE(AVG(EXTRACT(EPOCH FROM (last_sell - first_buy)) / 3600), 0)
    INTO avg_hold_time
    FROM wallet_hold_times;

    -- Calculate loyalty score (% of holders who haven't sold)
    WITH wallet_activity AS (
        SELECT
            wallet_address,
            SUM(CASE WHEN trade_type = 'buy' THEN volume_usd ELSE 0 END) as total_bought,
            SUM(CASE WHEN trade_type = 'sell' THEN volume_usd ELSE 0 END) as total_sold
        FROM token_trades
        WHERE token_address = p_token_address
        GROUP BY wallet_address
    )
    SELECT COUNT(CASE WHEN total_sold = 0 OR total_bought > total_sold THEN 1 END)::DECIMAL / COUNT(*)
    INTO loyalty_score
    FROM wallet_activity;

    -- New holders in last 24h (wallets with first trade in last 24h)
    WITH first_trades AS (
        SELECT wallet_address, MIN(trade_time) as first_trade
        FROM token_trades
        WHERE token_address = p_token_address
        GROUP BY wallet_address
    )
    SELECT COUNT(*) INTO new_24h
    FROM first_trades
    WHERE first_trade >= NOW() - INTERVAL '24 hours';

    -- Churned holders in last 24h (wallets who sold everything in last 24h)
    WITH wallet_balances AS (
        SELECT
            wallet_address,
            SUM(CASE WHEN trade_type = 'buy' THEN volume_usd ELSE -volume_usd END) as net_volume,
            MAX(CASE WHEN trade_type = 'sell' THEN trade_time END) as last_sell
        FROM token_trades
        WHERE token_address = p_token_address
        GROUP BY wallet_address
    )
    SELECT COUNT(*) INTO churned_24h
    FROM wallet_balances
    WHERE net_volume <= 0
    AND last_sell >= NOW() - INTERVAL '24 hours';

    -- Diamond hands ratio (wallets with only buys, no sells)
    WITH wallet_types AS (
        SELECT
            wallet_address,
            COUNT(CASE WHEN trade_type = 'buy' THEN 1 END) as buy_count,
            COUNT(CASE WHEN trade_type = 'sell' THEN 1 END) as sell_count
        FROM token_trades
        WHERE token_address = p_token_address
        GROUP BY wallet_address
    )
    SELECT COUNT(CASE WHEN buy_count > 0 AND sell_count = 0 THEN 1 END)::DECIMAL / COUNT(*)
    INTO diamond_hands
    FROM wallet_types;

    -- Paper hands ratio (wallets who sold within 1 hour of buying)
    WITH quick_sellers AS (
        SELECT DISTINCT t1.wallet_address
        FROM token_trades t1
        JOIN token_trades t2 ON t1.wallet_address = t2.wallet_address
        WHERE t1.token_address = p_token_address
        AND t2.token_address = p_token_address
        AND t1.trade_type = 'buy'
        AND t2.trade_type = 'sell'
        AND t2.trade_time BETWEEN t1.trade_time AND t1.trade_time + INTERVAL '1 hour'
    )
    SELECT COUNT(*)::DECIMAL / NULLIF(total_holders, 0)
    INTO paper_hands
    FROM quick_sellers;

    -- Whale and retail holder counts
    WITH holder_volumes AS (
        SELECT
            wallet_address,
            SUM(volume_usd) as total_volume
        FROM token_trades
        WHERE token_address = p_token_address
        GROUP BY wallet_address
    )
    SELECT
        COUNT(CASE WHEN total_volume >= 10000 THEN 1 END),
        COUNT(CASE WHEN total_volume < 100 THEN 1 END)
    INTO whale_holders, retail_holders
    FROM holder_volumes;

    -- Swing traders (wallets with multiple buy-sell cycles)
    WITH wallet_cycles AS (
        SELECT
            wallet_address,
            COUNT(CASE WHEN trade_type = 'buy' THEN 1 END) as buys,
            COUNT(CASE WHEN trade_type = 'sell' THEN 1 END) as sells
        FROM token_trades
        WHERE token_address = p_token_address
        GROUP BY wallet_address
    )
    SELECT COUNT(*) INTO swing_traders
    FROM wallet_cycles
    WHERE buys > 1 AND sells > 1;

    -- Early adopter ratio (entered in first 10% of token life)
    IF token_age_hours > 0 THEN
        WITH early_entries AS (
            SELECT wallet_address, MIN(trade_time) as first_entry
            FROM token_trades
            WHERE token_address = p_token_address
            GROUP BY wallet_address
        )
        SELECT COUNT(CASE WHEN first_entry <= token_start_time + (token_age_hours * 0.1 * INTERVAL '1 hour') THEN 1 END)::DECIMAL / COUNT(*)
        INTO early_adopters
        FROM early_entries;
    END IF;

    -- Calculate median holder value (simplified)
    WITH holder_values AS (
        SELECT
            wallet_address,
            SUM(CASE WHEN trade_type = 'buy' THEN volume_usd ELSE 0 END) as total_bought
        FROM token_trades
        WHERE token_address = p_token_address
        GROUP BY wallet_address
    )
    SELECT COALESCE(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY total_bought), 0)
    INTO median_value
    FROM holder_values;

    -- Set default values for complex calculations
    fomo_entries := 0; -- Placeholder
    panic_exits := 0; -- Placeholder
    concentration_top10 := 0; -- Placeholder
    gini_coeff := 0; -- Placeholder
    top_dominance := 0; -- Placeholder

    RETURN QUERY SELECT
        total_holders, turnover_rate, avg_hold_time, loyalty_score,
        new_24h, churned_24h, concentration_top10,
        diamond_hands, paper_hands,
        whale_holders, retail_holders, swing_traders,
        early_adopters, fomo_entries, panic_exits,
        gini_coeff, median_value, top_dominance;
END;
$$ LANGUAGE plpgsql;

-- Funzione standalone per estrarre e inserire le feature di migrazione
CREATE OR REPLACE FUNCTION extract_migration_features(
    p_token_address VARCHAR(44),
    p_creator_address VARCHAR(44),
    p_raw_data JSONB,
    p_created_at TIMESTAMP WITH TIME ZONE,
    p_updated_at TIMESTAMP WITH TIME ZONE
) RETURNS VOID AS $$
DECLARE
    feature_record RECORD;
    creator_stats creator_stats_type := ROW(0.0, 0, 0.0, 0);
    trading_features RECORD;
    holder_features RECORD;
    social_count INTEGER := 0;
    volatility DECIMAL(10,4) := 0;
    token_age INTEGER := 0;
    migration_label BOOLEAN := FALSE;
    migration_type_val VARCHAR(20) := NULL;
    migration_success INTEGER := 0;
    pool_elem RECORD;
BEGIN
    -- Estrai le feature principali dalla pool[0] (per compatibilità)
    SELECT
        COALESCE((p_raw_data->'token'->>'name'), '')::VARCHAR(100) as token_name,
        COALESCE((p_raw_data->'token'->>'symbol'), '')::VARCHAR(20) as token_symbol,
        COALESCE((p_raw_data->'token'->>'decimals')::INTEGER, 6) as token_decimals,
        COALESCE((p_raw_data->'pools'->0->'price_usd')::DECIMAL(20,10), 0) as current_price_usd,
        COALESCE((p_raw_data->'pools'->0->'marketCap_usd')::DECIMAL(20,2), 0) as market_cap_usd,
        COALESCE((p_raw_data->'pools'->0->'liquidity_usd')::DECIMAL(20,2), 0) as liquidity_usd,
        COALESCE((p_raw_data->'pools'->0->'txns'->'volume')::DECIMAL(20,2), 0) as volume_total,
        COALESCE((p_raw_data->'pools'->0->'curvePercentage')::DECIMAL(5,2), 0) as curve_percentage,
        (p_raw_data->'pools'->0->>'curve') as curve_address,
        COALESCE((p_raw_data->'pools'->0->>'market'), 'pumpfun') as market_type,
        COALESCE((p_raw_data->'pools'->0->'txns'->'total')::INTEGER, 0) as total_transactions,
        COALESCE((p_raw_data->'pools'->0->'txns'->'buys')::INTEGER, 0) as buy_transactions,
        COALESCE((p_raw_data->'pools'->0->'txns'->'sells')::INTEGER, 0) as sell_transactions,
        COALESCE((p_raw_data->>'holders')::INTEGER, 0) as unique_holders,
        COALESCE((p_raw_data->'events'->'1m_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_1m,
        COALESCE((p_raw_data->'events'->'5m_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_5m,
        COALESCE((p_raw_data->'events'->'15m_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_15m,
        COALESCE((p_raw_data->'events'->'30m_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_30m,
        COALESCE((p_raw_data->'events'->'1h_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_1h,
        COALESCE((p_raw_data->'events'->'2h_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_2h,
        COALESCE((p_raw_data->'events'->'6h_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_6h,
        COALESCE((p_raw_data->'events'->'12h_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_12h,
        COALESCE((p_raw_data->'events'->'24h_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_24h,
        COALESCE((p_raw_data->'risk'->>'score')::INTEGER, 0) as risk_score,
        COALESCE((p_raw_data->'risk'->>'rugged')::BOOLEAN, FALSE) as is_rugged,
        COALESCE((p_raw_data->'token'->>'createdOn'), '') as created_on_platform,
        COALESCE((p_raw_data->'pools'->0->'lpBurn')::DECIMAL(5,2), 0) as lp_burn_percentage,
        (p_raw_data->'pools'->0->>'bundleId') as bundle_id
    INTO feature_record;

    -- Determina la migrazione su tutte le pool
    migration_label := FALSE;
    migration_type_val := NULL;
    FOR pool_elem IN SELECT pool FROM jsonb_array_elements(p_raw_data->'pools') AS pools(pool) LOOP
        IF pool_elem.pool->>'market' IS NOT NULL AND pool_elem.pool->>'market' <> 'pumpfun' THEN
            migration_label := TRUE;
            migration_type_val := pool_elem.pool->>'market';
            EXIT;
        END IF;
    END LOOP;
    IF migration_label THEN
        migration_success := 1;
    ELSE
        migration_success := 0;
    END IF;
    
    -- Calculate social media presence
    social_count := 0;
    IF (p_raw_data->'token'->>'twitter') IS NOT NULL AND (p_raw_data->'token'->>'twitter') != '' THEN
        social_count := social_count + 1;
    END IF;
    IF (p_raw_data->'token'->>'telegram') IS NOT NULL AND (p_raw_data->'token'->>'telegram') != '' THEN
        social_count := social_count + 1;
    END IF;
    IF (p_raw_data->'token'->>'website') IS NOT NULL AND (p_raw_data->'token'->>'website') != '' THEN
        social_count := social_count + 1;
    END IF;
    -- Add more social media checks...
    
    -- Calculate volatility (standard deviation of price changes)
    volatility := SQRT(
        (POWER(COALESCE(feature_record.price_change_1h, 0), 2) + 
         POWER(COALESCE(feature_record.price_change_2h, 0), 2) + 
         POWER(COALESCE(feature_record.price_change_6h, 0), 2) + 
         POWER(COALESCE(feature_record.price_change_12h, 0), 2) + 
         POWER(COALESCE(feature_record.price_change_24h, 0), 2)) / 5.0
    );
    
    -- Calculate token age in hours
    IF (p_raw_data->'token'->'creation'->>'created_time') IS NOT NULL THEN
        token_age := EXTRACT(EPOCH FROM (NOW() - TO_TIMESTAMP((p_raw_data->'token'->'creation'->>'created_time')::BIGINT))) / 3600;
    END IF;

    -- Get creator statistics (if creator exists)
    IF p_creator_address IS NOT NULL THEN
        SELECT * INTO creator_stats FROM calculate_creator_stats(p_creator_address);
    ELSE
        -- Initialize creator_stats by assigning values to each field explicitly
        creator_stats.success_rate := 0.0;
        creator_stats.total_tokens := 0;
        creator_stats.avg_volume := 0.0;
        creator_stats.reputation_score := 0;
    END IF;

    -- Get advanced trading features from trades data
    SELECT * INTO trading_features FROM calculate_trading_features(p_token_address);

    -- Get holder analysis features from trades data
    SELECT * INTO holder_features FROM calculate_holder_features(p_token_address);

    -- Insert or update migration features
    INSERT INTO token_migration_features (
        token_address, creator_address, token_name, token_symbol, token_decimals,
        current_price_usd, market_cap_usd, liquidity_usd, volume_total,
        total_transactions, buy_transactions, sell_transactions, unique_holders,
        -- Advanced trading features
        total_trade_volume_usd, avg_trade_size_usd, median_trade_size_usd, max_single_trade_usd,
        buy_sell_ratio, buy_volume_ratio, net_volume_flow,
        unique_buyers, unique_sellers, whale_trades_count, retail_trades_count,
        trades_last_1h, trades_last_6h, trades_last_24h,
        volume_last_1h, volume_last_6h, volume_last_24h,
        trading_velocity, volume_velocity, momentum_score,
        suspected_mm_trades, retail_participation_ratio,
        -- Holder features
        total_unique_holders, holder_turnover_rate, avg_hold_time_hours, holder_loyalty_score,
        new_holders_24h, churned_holders_24h, holder_concentration_top10,
        diamond_hands_ratio, paper_hands_ratio,
        whale_holder_count, retail_holder_count, swing_trader_count,
        early_adopter_ratio, fomo_entry_ratio, panic_exit_ratio,
        holder_gini_coefficient, median_holder_value_usd, top_holder_dominance,
        curve_percentage, has_curve, curve_address,
        price_change_1m, price_change_5m, price_change_15m, price_change_30m,
        price_change_1h, price_change_2h, price_change_6h, price_change_12h, price_change_24h,
        risk_score, is_rugged,
        has_twitter, has_telegram, has_website,
        created_on_platform, lp_burn_percentage,
        -- Creator historical features
        creator_success_rate, creator_total_tokens, creator_avg_volume_at_migration, creator_reputation_score,
        -- Migration labels
        did_migrate, migration_type, migration_success_level, final_market_type, has_bundle_id,
        volume_to_mcap_ratio, social_score, volatility_score, token_age_hours,
        updated_at
    ) VALUES (
        p_token_address, p_creator_address,
        feature_record.token_name, feature_record.token_symbol, feature_record.token_decimals,
        feature_record.current_price_usd, feature_record.market_cap_usd,
        feature_record.liquidity_usd, feature_record.volume_total,
        feature_record.total_transactions, feature_record.buy_transactions,
        feature_record.sell_transactions, feature_record.unique_holders,
        -- Advanced trading features
        COALESCE(trading_features.total_trade_volume_usd, 0),
        COALESCE(trading_features.avg_trade_size_usd, 0),
        COALESCE(trading_features.median_trade_size_usd, 0),
        COALESCE(trading_features.max_single_trade_usd, 0),
        COALESCE(trading_features.buy_sell_ratio, 0),
        COALESCE(trading_features.buy_volume_ratio, 0),
        COALESCE(trading_features.net_volume_flow, 0),
        COALESCE(trading_features.unique_buyers, 0),
        COALESCE(trading_features.unique_sellers, 0),
        COALESCE(trading_features.whale_trades_count, 0),
        COALESCE(trading_features.retail_trades_count, 0),
        COALESCE(trading_features.trades_last_1h, 0),
        COALESCE(trading_features.trades_last_6h, 0),
        COALESCE(trading_features.trades_last_24h, 0),
        COALESCE(trading_features.volume_last_1h, 0),
        COALESCE(trading_features.volume_last_6h, 0),
        COALESCE(trading_features.volume_last_24h, 0),
        COALESCE(trading_features.trading_velocity, 0),
        COALESCE(trading_features.volume_velocity, 0),
        COALESCE(trading_features.momentum_score, 0),
        COALESCE(trading_features.suspected_mm_trades, 0),
        COALESCE(trading_features.retail_participation_ratio, 0),
        -- Holder features
        COALESCE(holder_features.total_unique_holders, 0),
        COALESCE(holder_features.holder_turnover_rate, 0),
        COALESCE(holder_features.avg_hold_time_hours, 0),
        COALESCE(holder_features.holder_loyalty_score, 0),
        COALESCE(holder_features.new_holders_24h, 0),
        COALESCE(holder_features.churned_holders_24h, 0),
        COALESCE(holder_features.holder_concentration_top10, 0),
        COALESCE(holder_features.diamond_hands_ratio, 0),
        COALESCE(holder_features.paper_hands_ratio, 0),
        COALESCE(holder_features.whale_holder_count, 0),
        COALESCE(holder_features.retail_holder_count, 0),
        COALESCE(holder_features.swing_trader_count, 0),
        COALESCE(holder_features.early_adopter_ratio, 0),
        COALESCE(holder_features.fomo_entry_ratio, 0),
        COALESCE(holder_features.panic_exit_ratio, 0),
        COALESCE(holder_features.holder_gini_coefficient, 0),
        COALESCE(holder_features.median_holder_value_usd, 0),
        COALESCE(holder_features.top_holder_dominance, 0),
        feature_record.curve_percentage,
        (feature_record.curve_address IS NOT NULL),
        feature_record.curve_address,
        feature_record.price_change_1m, feature_record.price_change_5m, feature_record.price_change_15m,
        feature_record.price_change_30m, feature_record.price_change_1h, feature_record.price_change_2h,
        feature_record.price_change_6h, feature_record.price_change_12h, feature_record.price_change_24h,
        feature_record.risk_score, feature_record.is_rugged,
        (p_raw_data->'token'->>'twitter') IS NOT NULL,
        (p_raw_data->'token'->>'telegram') IS NOT NULL,
        (p_raw_data->'token'->>'website') IS NOT NULL,
        feature_record.created_on_platform, feature_record.lp_burn_percentage,
        -- Creator historical features
        creator_stats.success_rate, creator_stats.total_tokens, creator_stats.avg_volume, creator_stats.reputation_score,
        -- Migration labels (CORE TARGET VARIABLES)
        migration_label, migration_type_val, migration_success, migration_type_val, (feature_record.bundle_id IS NOT NULL),
        -- Derived features
        CASE WHEN feature_record.market_cap_usd > 0 THEN feature_record.volume_total / feature_record.market_cap_usd ELSE 0 END,
        social_count,
        volatility,
        token_age,
        NOW()
    )
    ON CONFLICT (token_address) DO UPDATE SET
        creator_address = EXCLUDED.creator_address,
        token_name = EXCLUDED.token_name,
        token_symbol = EXCLUDED.token_symbol,
        current_price_usd = EXCLUDED.current_price_usd,
        market_cap_usd = EXCLUDED.market_cap_usd,
        liquidity_usd = EXCLUDED.liquidity_usd,
        volume_total = EXCLUDED.volume_total,
        total_transactions = EXCLUDED.total_transactions,
        buy_transactions = EXCLUDED.buy_transactions,
        sell_transactions = EXCLUDED.sell_transactions,
        unique_holders = EXCLUDED.unique_holders,
        curve_percentage = EXCLUDED.curve_percentage,
        has_curve = EXCLUDED.has_curve,
        curve_address = EXCLUDED.curve_address,
        -- Update migration status (IMPORTANTE: può cambiare nel tempo)
        did_migrate = EXCLUDED.did_migrate,
        migration_type = EXCLUDED.migration_type,
        migration_success_level = EXCLUDED.migration_success_level,
        final_market_type = EXCLUDED.final_market_type,
        has_bundle_id = EXCLUDED.has_bundle_id,
        -- Update creator stats
        creator_success_rate = EXCLUDED.creator_success_rate,
        creator_total_tokens = EXCLUDED.creator_total_tokens,
        creator_avg_volume_at_migration = EXCLUDED.creator_avg_volume_at_migration,
        creator_reputation_score = EXCLUDED.creator_reputation_score,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to extract and calculate migration features from raw token data
CREATE OR REPLACE FUNCTION update_migration_features()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM extract_migration_features(
        NEW.token_address,
        NEW.creator_address,
        NEW.raw_data,
        NEW.created_at,
        NEW.updated_at
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 📥 Function to insert trades data from SolanaTracker API
CREATE OR REPLACE FUNCTION insert_trades_from_api(
    p_token_address VARCHAR(44),
    p_trades_json JSONB
) RETURNS INTEGER AS $$
DECLARE
    trade_elem JSONB;
    trades_inserted INTEGER := 0;
    trade_size_cat VARCHAR(20);
    trade_timestamp TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Loop through each trade in the JSON array
    FOR trade_elem IN SELECT value FROM jsonb_array_elements(p_trades_json->'trades') LOOP
        -- Determine trade size category
        CASE
            WHEN (trade_elem->>'volume')::DECIMAL < 10 THEN trade_size_cat := 'micro';
            WHEN (trade_elem->>'volume')::DECIMAL < 100 THEN trade_size_cat := 'small';
            WHEN (trade_elem->>'volume')::DECIMAL < 1000 THEN trade_size_cat := 'medium';
            WHEN (trade_elem->>'volume')::DECIMAL < 10000 THEN trade_size_cat := 'large';
            ELSE trade_size_cat := 'whale';
        END CASE;

        -- Convert timestamp from milliseconds to timestamp
        trade_timestamp := TO_TIMESTAMP((trade_elem->>'time')::BIGINT / 1000);

        -- Insert trade data
        INSERT INTO token_trades (
            token_address, tx_hash, trade_time, trade_type, wallet_address,
            token_amount, price_usd, volume_usd, volume_sol,
            program, pools, trade_size_category, raw_data
        ) VALUES (
            p_token_address,
            trade_elem->>'tx',
            trade_timestamp,
            trade_elem->>'type',
            trade_elem->>'wallet',
            (trade_elem->>'amount')::DECIMAL,
            (trade_elem->>'priceUsd')::DECIMAL,
            (trade_elem->>'volume')::DECIMAL,
            (trade_elem->>'volumeSol')::DECIMAL,
            trade_elem->>'program',
            trade_elem->'pools',
            trade_size_cat,
            trade_elem
        )
        ON CONFLICT (tx_hash) DO UPDATE SET
            trade_time = EXCLUDED.trade_time,
            trade_type = EXCLUDED.trade_type,
            wallet_address = EXCLUDED.wallet_address,
            token_amount = EXCLUDED.token_amount,
            price_usd = EXCLUDED.price_usd,
            volume_usd = EXCLUDED.volume_usd,
            volume_sol = EXCLUDED.volume_sol,
            program = EXCLUDED.program,
            pools = EXCLUDED.pools,
            trade_size_category = EXCLUDED.trade_size_category,
            raw_data = EXCLUDED.raw_data;

        trades_inserted := trades_inserted + 1;
    END LOOP;

    RETURN trades_inserted;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update migration features
CREATE TRIGGER token_migration_trigger
    AFTER INSERT OR UPDATE ON tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_migration_features();

-- Create a view for ML-ready data (normalized features for migration prediction)
CREATE OR REPLACE VIEW ml_migration_training_data AS
SELECT
    token_address,
    creator_address,

    -- NORMALIZED NUMERICAL FEATURES (0-1 scale)
    -- Market features
    CASE WHEN market_cap_usd > 0 THEN
        LEAST(LOG(market_cap_usd + 1) / 20.0, 1.0)
    ELSE 0 END as market_cap_normalized,

    CASE WHEN volume_total > 0 THEN
        LEAST(LOG(volume_total + 1) / 18.0, 1.0)
    ELSE 0 END as volume_normalized,

    CASE WHEN liquidity_usd > 0 THEN
        LEAST(LOG(liquidity_usd + 1) / 16.0, 1.0)
    ELSE 0 END as liquidity_normalized,

    -- Bonding curve features (CRITICAL for migration prediction)
    curve_percentage / 100.0 as curve_completion_normalized,

    -- Creator features (HISTORICAL PATTERNS)
    creator_success_rate / 100.0 as creator_success_rate_normalized,
    creator_reputation_score / 100.0 as creator_reputation_score_normalized,

    -- 🔥 ADVANCED TRADING FEATURES (normalized)
    -- Volume and transaction patterns
    CASE WHEN total_trade_volume_usd > 0 THEN
        LEAST(LOG(total_trade_volume_usd + 1) / 20.0, 1.0)
    ELSE 0 END as total_trade_volume_normalized,

    CASE WHEN avg_trade_size_usd > 0 THEN
        LEAST(LOG(avg_trade_size_usd + 1) / 15.0, 1.0)
    ELSE 0 END as avg_trade_size_normalized,

    -- Trading dynamics
    LEAST(buy_sell_ratio / 10.0, 1.0) as buy_sell_ratio_normalized,
    buy_volume_ratio as buy_volume_ratio_normalized, -- Already 0-1

    -- Wallet diversity
    CASE WHEN unique_buyers + unique_sellers > 0 THEN
        LEAST(LOG(unique_buyers + unique_sellers + 1) / 10.0, 1.0)
    ELSE 0 END as wallet_diversity_normalized,

    -- Trading velocity and momentum
    LEAST(trading_velocity / 100.0, 1.0) as trading_velocity_normalized,
    LEAST(momentum_score / 5.0, 1.0) as momentum_score_normalized,

    -- Market participation patterns
    retail_participation_ratio as retail_participation_normalized, -- Already 0-1
    CASE WHEN whale_trades_count + retail_trades_count > 0 THEN
        whale_trades_count::DECIMAL / (whale_trades_count + retail_trades_count)
    ELSE 0 END as whale_participation_ratio,

    -- Social media presence (count of platforms)
    social_score / 8.0 as social_score_normalized,

    -- Time-based features
    token_age_hours / 720.0 as token_age_hours_normalized,
    last_activity_hours / 720.0 as last_activity_hours_normalized,

    -- Categorical features (one-hot encoded)
    CASE WHEN risk_level_encoded = 0 THEN 1 ELSE 0 END as risk_level_low,
    CASE WHEN risk_level_encoded = 1 THEN 1 ELSE 0 END as risk_level_medium,
    CASE WHEN risk_level_encoded = 2 THEN 1 ELSE 0 END as risk_level_high,
    
    CASE WHEN market_tier = 0 THEN 1 ELSE 0 END as market_tier_micro,
    CASE WHEN market_tier = 1 THEN 1 ELSE 0 END as market_tier_small,
    CASE WHEN market_tier = 2 THEN 1 ELSE 0 END as market_tier_medium,
    CASE WHEN market_tier = 3 THEN 1 ELSE 0 END as market_tier_large,

    -- TARGET VARIABLES (migration labels)
    did_migrate,
    migration_success_level
FROM token_migration_features
WHERE feature_version = 1;
