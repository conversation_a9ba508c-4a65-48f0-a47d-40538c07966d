-- 🚀 ML Features Schema for Solana Token Migration Prediction
-- Predicts which tokens will successfully migrate from pump.fun to other DEXs
-- Focus: Early detection system for token migration opportunities

-- Drop existing objects if they exist
DROP TRIGGER IF EXISTS token_migration_trigger ON tokens;
DROP FUNCTION IF EXISTS update_migration_features();
DROP FUNCTION IF EXISTS calculate_creator_stats();
DROP TABLE IF EXISTS token_migration_features CASCADE;

-- Create the features table for migration prediction
CREATE TABLE token_migration_features (
    -- Primary identifiers
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(44) NOT NULL UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Basic token info
    creator_address VARCHAR(44),
    token_name VARCHAR(100),
    token_symbol VARCHAR(20),
    token_decimals INTEGER,
    
    -- Market data features (snapshot al momento della raccolta)
    current_price_usd DECIMAL(20,10),
    market_cap_usd DECIMAL(20,2),
    liquidity_usd DECIMAL(20,2),
    volume_total DECIMAL(20,2), -- volume totale dalla creazione

    -- Trading activity features
    total_transactions INTEGER DEFAULT 0,
    buy_transactions INTEGER DEFAULT 0,
    sell_transactions INTEGER DEFAULT 0,
    unique_holders INTEGER DEFAULT 0,

    -- Bonding curve features (chiave per migrazione)
    curve_percentage DECIMAL(5,2), -- % completamento bonding curve
    has_curve BOOLEAN DEFAULT TRUE, -- se ha ancora bonding curve attiva
    curve_address VARCHAR(44), -- indirizzo della bonding curve
    
    -- Price change features (for trend analysis)
    price_change_1m DECIMAL(10,4),
    price_change_5m DECIMAL(10,4),
    price_change_15m DECIMAL(10,4),
    price_change_30m DECIMAL(10,4),
    price_change_1h DECIMAL(10,4),
    price_change_2h DECIMAL(10,4),
    price_change_6h DECIMAL(10,4),
    price_change_12h DECIMAL(10,4),
    price_change_24h DECIMAL(10,4),
    
    -- Risk assessment features
    risk_score INTEGER DEFAULT 0,
    is_rugged BOOLEAN DEFAULT FALSE,
    has_social_media BOOLEAN DEFAULT FALSE,
    bonding_curve_complete BOOLEAN DEFAULT FALSE,
    
    -- Social media presence (boolean features)
    has_twitter BOOLEAN DEFAULT FALSE,
    has_telegram BOOLEAN DEFAULT FALSE,
    has_website BOOLEAN DEFAULT FALSE,
    has_discord BOOLEAN DEFAULT FALSE,
    has_instagram BOOLEAN DEFAULT FALSE,
    has_youtube BOOLEAN DEFAULT FALSE,
    has_reddit BOOLEAN DEFAULT FALSE,
    has_github BOOLEAN DEFAULT FALSE,
    
    -- Platform features
    created_on_platform VARCHAR(50),
    market_type VARCHAR(50),
    lp_burn_percentage DECIMAL(5,2) DEFAULT 0, -- % LP bruciato
    
    -- Derived features for ML
    volume_to_mcap_ratio DECIMAL(10,6),
    holder_concentration DECIMAL(10,6), -- top holder percentage
    social_score INTEGER DEFAULT 0, -- count of social media platforms
    volatility_score DECIMAL(10,4), -- based on price changes
    
    -- Time-based features
    token_age_hours INTEGER, -- hours since creation
    last_activity_hours INTEGER, -- hours since last transaction
    
    -- Categorical features (encoded)
    risk_level_encoded INTEGER DEFAULT 0, -- 0=low, 1=medium, 2=high
    market_tier INTEGER DEFAULT 0, -- 0=micro, 1=small, 2=medium, 3=large
    
    -- CREATOR HISTORICAL FEATURES (Sistema 1)
    creator_success_rate DECIMAL(5,4), -- % of creator's tokens that migrated
    creator_total_tokens INTEGER DEFAULT 0, -- total tokens created by this creator
    creator_avg_migration_time INTEGER, -- average hours to migration for creator
    creator_avg_volume_at_migration DECIMAL(20,2), -- average volume when creator's tokens migrate
    creator_reputation_score INTEGER DEFAULT 0, -- 0-100 based on historical success

    -- MIGRATION PREDICTION TARGETS (Labels for ML)
    did_migrate BOOLEAN DEFAULT FALSE, -- TRUE if token migrated to other DEX
    migration_type VARCHAR(20), -- 'pumpfun-amm', 'raydium', 'meteora-dyn', etc.
    migration_success_level INTEGER DEFAULT 0, -- 0=no migration, 1=low volume, 2=medium, 3=high volume
    estimated_hours_to_migration INTEGER, -- stima ore dalla creazione alla migrazione
    volume_at_snapshot DECIMAL(20,2), -- volume al momento dello snapshot

    -- SUCCESS INDICATORS (per calcolare le label)
    final_market_type VARCHAR(20), -- tipo di mercato finale
    has_bundle_id BOOLEAN DEFAULT FALSE, -- se ha bundle ID (indica migrazione)
    
    -- Metadata
    feature_version INTEGER DEFAULT 1,
    last_ml_update TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX idx_migration_features_token_address ON token_migration_features(token_address);
CREATE INDEX idx_migration_features_creator ON token_migration_features(creator_address);
CREATE INDEX idx_migration_features_market_cap ON token_migration_features(market_cap_usd);
CREATE INDEX idx_migration_features_volume ON token_migration_features(volume_total);
CREATE INDEX idx_migration_features_risk ON token_migration_features(risk_score);
CREATE INDEX idx_migration_features_created_at ON token_migration_features(created_at);
CREATE INDEX idx_migration_features_migration ON token_migration_features(did_migrate);
CREATE INDEX idx_migration_features_creator_success ON token_migration_features(creator_success_rate);

-- Composite indexes for ML queries
CREATE INDEX idx_migration_features_ml_training ON token_migration_features(
    market_cap_usd, volume_total, risk_score, social_score, creator_success_rate
) WHERE did_migrate IS NOT NULL;

CREATE INDEX idx_migration_features_migrated_tokens ON token_migration_features(
    created_at, migration_type, volume_total
) WHERE did_migrate = TRUE;

CREATE INDEX idx_migration_features_curve_completion ON token_migration_features(
    curve_percentage, has_curve, liquidity_usd
) WHERE curve_percentage > 90;

-- Function to calculate creator historical statistics
CREATE OR REPLACE FUNCTION calculate_creator_stats(creator_addr VARCHAR(44))
RETURNS TABLE(
    success_rate DECIMAL(5,4),
    total_tokens INTEGER,
    avg_volume DECIMAL(20,2),
    reputation_score INTEGER
) AS $$
DECLARE
    total_count INTEGER := 0;
    migrated_count INTEGER := 0;
    avg_vol DECIMAL(20,2) := 0;
    reputation INTEGER := 0;
BEGIN
    -- Count total tokens by creator
    SELECT COUNT(*) INTO total_count
    FROM tokens
    WHERE creator_address = creator_addr;

    -- Count migrated tokens by creator
    SELECT COUNT(*) INTO migrated_count
    FROM tokens
    WHERE creator_address = creator_addr
    AND (raw_data->'pools'->0->>'market') IN ('pumpfun-amm', 'raydium', 'meteora-dyn', 'meteora-dyn-v2', 'raydium-cpmm');

    -- Calculate average volume for migrated tokens
    SELECT COALESCE(AVG((raw_data->'pools'->0->'txns'->'volume')::DECIMAL), 0) INTO avg_vol
    FROM tokens
    WHERE creator_address = creator_addr
    AND (raw_data->'pools'->0->>'market') IN ('pumpfun-amm', 'raydium', 'meteora-dyn', 'meteora-dyn-v2', 'raydium-cpmm');

    -- Calculate reputation score (0-100)
    IF total_count > 0 THEN
        reputation := LEAST(100,
            (migrated_count * 50 / total_count) + -- 50% weight on success rate
            (CASE WHEN total_count >= 10 THEN 25 ELSE total_count * 2.5 END) + -- 25% weight on experience
            (CASE WHEN avg_vol > 100000 THEN 25 ELSE LEAST(25, avg_vol / 4000) END) -- 25% weight on volume
        );
    END IF;

    RETURN QUERY SELECT
        CASE WHEN total_count > 0 THEN migrated_count::DECIMAL / total_count ELSE 0 END,
        total_count,
        avg_vol,
        reputation;
END;
$$ LANGUAGE plpgsql;

-- Funzione standalone per estrarre e inserire le feature di migrazione
CREATE OR REPLACE FUNCTION extract_migration_features(
    p_token_address VARCHAR(44),
    p_creator_address VARCHAR(44),
    p_raw_data JSONB,
    p_created_at TIMESTAMP WITH TIME ZONE,
    p_updated_at TIMESTAMP WITH TIME ZONE
) RETURNS VOID AS $$
DECLARE
    feature_record RECORD;
    creator_stats creator_stats_type := ROW(0.0, 0, 0.0, 0);
    social_count INTEGER := 0;
    volatility DECIMAL(10,4) := 0;
    token_age INTEGER := 0;
    migration_label BOOLEAN := FALSE;
    migration_type_val VARCHAR(20) := NULL;
    migration_success INTEGER := 0;
    pool_elem RECORD;
BEGIN
    -- Estrai le feature principali dalla pool[0] (per compatibilità)
    SELECT
        COALESCE((p_raw_data->'token'->>'name'), '')::VARCHAR(100) as token_name,
        COALESCE((p_raw_data->'token'->>'symbol'), '')::VARCHAR(20) as token_symbol,
        COALESCE((p_raw_data->'token'->>'decimals')::INTEGER, 6) as token_decimals,
        COALESCE((p_raw_data->'pools'->0->'price_usd')::DECIMAL(20,10), 0) as current_price_usd,
        COALESCE((p_raw_data->'pools'->0->'marketCap_usd')::DECIMAL(20,2), 0) as market_cap_usd,
        COALESCE((p_raw_data->'pools'->0->'liquidity_usd')::DECIMAL(20,2), 0) as liquidity_usd,
        COALESCE((p_raw_data->'pools'->0->'txns'->'volume')::DECIMAL(20,2), 0) as volume_total,
        COALESCE((p_raw_data->'pools'->0->'curvePercentage')::DECIMAL(5,2), 0) as curve_percentage,
        (p_raw_data->'pools'->0->>'curve') as curve_address,
        COALESCE((p_raw_data->'pools'->0->>'market'), 'pumpfun') as market_type,
        COALESCE((p_raw_data->'pools'->0->'txns'->'total')::INTEGER, 0) as total_transactions,
        COALESCE((p_raw_data->'pools'->0->'txns'->'buys')::INTEGER, 0) as buy_transactions,
        COALESCE((p_raw_data->'pools'->0->'txns'->'sells')::INTEGER, 0) as sell_transactions,
        COALESCE((p_raw_data->>'holders')::INTEGER, 0) as unique_holders,
        COALESCE((p_raw_data->'events'->'1m_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_1m,
        COALESCE((p_raw_data->'events'->'5m_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_5m,
        COALESCE((p_raw_data->'events'->'15m_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_15m,
        COALESCE((p_raw_data->'events'->'30m_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_30m,
        COALESCE((p_raw_data->'events'->'1h_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_1h,
        COALESCE((p_raw_data->'events'->'2h_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_2h,
        COALESCE((p_raw_data->'events'->'6h_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_6h,
        COALESCE((p_raw_data->'events'->'12h_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_12h,
        COALESCE((p_raw_data->'events'->'24h_priceChangePercentage')::DECIMAL(10,4), 0) as price_change_24h,
        COALESCE((p_raw_data->'risk'->>'score')::INTEGER, 0) as risk_score,
        COALESCE((p_raw_data->'risk'->>'rugged')::BOOLEAN, FALSE) as is_rugged,
        COALESCE((p_raw_data->'token'->>'createdOn'), '') as created_on_platform,
        COALESCE((p_raw_data->'pools'->0->'lpBurn')::DECIMAL(5,2), 0) as lp_burn_percentage,
        (p_raw_data->'pools'->0->>'bundleId') as bundle_id
    INTO feature_record;

    -- Determina la migrazione su tutte le pool
    migration_label := FALSE;
    migration_type_val := NULL;
    FOR pool_elem IN SELECT pool FROM jsonb_array_elements(p_raw_data->'pools') AS pools(pool) LOOP
        IF pool_elem.pool->>'market' IS NOT NULL AND pool_elem.pool->>'market' <> 'pumpfun' THEN
            migration_label := TRUE;
            migration_type_val := pool_elem.pool->>'market';
            EXIT;
        END IF;
    END LOOP;
    IF migration_label THEN
        migration_success := 1;
    ELSE
        migration_success := 0;
    END IF;
    
    -- Calculate social media presence
    social_count := 0;
    IF (p_raw_data->'token'->>'twitter') IS NOT NULL AND (p_raw_data->'token'->>'twitter') != '' THEN
        social_count := social_count + 1;
    END IF;
    IF (p_raw_data->'token'->>'telegram') IS NOT NULL AND (p_raw_data->'token'->>'telegram') != '' THEN
        social_count := social_count + 1;
    END IF;
    IF (p_raw_data->'token'->>'website') IS NOT NULL AND (p_raw_data->'token'->>'website') != '' THEN
        social_count := social_count + 1;
    END IF;
    -- Add more social media checks...
    
    -- Calculate volatility (standard deviation of price changes)
    volatility := SQRT(
        (POWER(COALESCE(feature_record.price_change_1h, 0), 2) + 
         POWER(COALESCE(feature_record.price_change_2h, 0), 2) + 
         POWER(COALESCE(feature_record.price_change_6h, 0), 2) + 
         POWER(COALESCE(feature_record.price_change_12h, 0), 2) + 
         POWER(COALESCE(feature_record.price_change_24h, 0), 2)) / 5.0
    );
    
    -- Calculate token age in hours
    IF (p_raw_data->'token'->'creation'->>'created_time') IS NOT NULL THEN
        token_age := EXTRACT(EPOCH FROM (NOW() - TO_TIMESTAMP((p_raw_data->'token'->'creation'->>'created_time')::BIGINT))) / 3600;
    END IF;

    -- Get creator statistics (if creator exists)
    IF p_creator_address IS NOT NULL THEN
        SELECT * INTO creator_stats FROM calculate_creator_stats(p_creator_address);
    ELSE
        -- Initialize creator_stats by assigning values to each field explicitly
        creator_stats.success_rate := 0.0;
        creator_stats.total_tokens := 0;
        creator_stats.avg_volume := 0.0;
        creator_stats.reputation_score := 0;
    END IF;

    -- Insert or update migration features
    INSERT INTO token_migration_features (
        token_address, creator_address, token_name, token_symbol, token_decimals,
        current_price_usd, market_cap_usd, liquidity_usd, volume_total,
        total_transactions, buy_transactions, sell_transactions, unique_holders,
        curve_percentage, has_curve, curve_address,
        price_change_1m, price_change_5m, price_change_15m, price_change_30m,
        price_change_1h, price_change_2h, price_change_6h, price_change_12h, price_change_24h,
        risk_score, is_rugged,
        has_twitter, has_telegram, has_website,
        created_on_platform, lp_burn_percentage,
        -- Creator historical features
        creator_success_rate, creator_total_tokens, creator_avg_volume_at_migration, creator_reputation_score,
        -- Migration labels
        did_migrate, migration_type, migration_success_level, final_market_type, has_bundle_id,
        volume_to_mcap_ratio, social_score, volatility_score, token_age_hours,
        updated_at
    ) VALUES (
        p_token_address, p_creator_address,
        feature_record.token_name, feature_record.token_symbol, feature_record.token_decimals,
        feature_record.current_price_usd, feature_record.market_cap_usd,
        feature_record.liquidity_usd, feature_record.volume_total,
        feature_record.total_transactions, feature_record.buy_transactions,
        feature_record.sell_transactions, feature_record.unique_holders,
        feature_record.curve_percentage,
        (feature_record.curve_address IS NOT NULL),
        feature_record.curve_address,
        feature_record.price_change_1m, feature_record.price_change_5m, feature_record.price_change_15m,
        feature_record.price_change_30m, feature_record.price_change_1h, feature_record.price_change_2h,
        feature_record.price_change_6h, feature_record.price_change_12h, feature_record.price_change_24h,
        feature_record.risk_score, feature_record.is_rugged,
        (p_raw_data->'token'->>'twitter') IS NOT NULL,
        (p_raw_data->'token'->>'telegram') IS NOT NULL,
        (p_raw_data->'token'->>'website') IS NOT NULL,
        feature_record.created_on_platform, feature_record.lp_burn_percentage,
        -- Creator historical features
        creator_stats.success_rate, creator_stats.total_tokens, creator_stats.avg_volume, creator_stats.reputation_score,
        -- Migration labels (CORE TARGET VARIABLES)
        migration_label, migration_type_val, migration_success, migration_type_val, (feature_record.bundle_id IS NOT NULL),
        -- Derived features
        CASE WHEN feature_record.market_cap_usd > 0 THEN feature_record.volume_total / feature_record.market_cap_usd ELSE 0 END,
        social_count,
        volatility,
        token_age,
        NOW()
    )
    ON CONFLICT (token_address) DO UPDATE SET
        creator_address = EXCLUDED.creator_address,
        token_name = EXCLUDED.token_name,
        token_symbol = EXCLUDED.token_symbol,
        current_price_usd = EXCLUDED.current_price_usd,
        market_cap_usd = EXCLUDED.market_cap_usd,
        liquidity_usd = EXCLUDED.liquidity_usd,
        volume_total = EXCLUDED.volume_total,
        total_transactions = EXCLUDED.total_transactions,
        buy_transactions = EXCLUDED.buy_transactions,
        sell_transactions = EXCLUDED.sell_transactions,
        unique_holders = EXCLUDED.unique_holders,
        curve_percentage = EXCLUDED.curve_percentage,
        has_curve = EXCLUDED.has_curve,
        curve_address = EXCLUDED.curve_address,
        -- Update migration status (IMPORTANTE: può cambiare nel tempo)
        did_migrate = EXCLUDED.did_migrate,
        migration_type = EXCLUDED.migration_type,
        migration_success_level = EXCLUDED.migration_success_level,
        final_market_type = EXCLUDED.final_market_type,
        has_bundle_id = EXCLUDED.has_bundle_id,
        -- Update creator stats
        creator_success_rate = EXCLUDED.creator_success_rate,
        creator_total_tokens = EXCLUDED.creator_total_tokens,
        creator_avg_volume_at_migration = EXCLUDED.creator_avg_volume_at_migration,
        creator_reputation_score = EXCLUDED.creator_reputation_score,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to extract and calculate migration features from raw token data
CREATE OR REPLACE FUNCTION update_migration_features()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM extract_migration_features(
        NEW.token_address,
        NEW.creator_address,
        NEW.raw_data,
        NEW.created_at,
        NEW.updated_at
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update migration features
CREATE TRIGGER token_migration_trigger
    AFTER INSERT OR UPDATE ON tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_migration_features();

-- Create a view for ML-ready data (normalized features for migration prediction)
CREATE OR REPLACE VIEW ml_migration_training_data AS
SELECT
    token_address,
    creator_address,

    -- NORMALIZED NUMERICAL FEATURES (0-1 scale)
    -- Market features
    CASE WHEN market_cap_usd > 0 THEN
        LEAST(LOG(market_cap_usd + 1) / 20.0, 1.0)
    ELSE 0 END as market_cap_normalized,

    CASE WHEN volume_total > 0 THEN
        LEAST(LOG(volume_total + 1) / 18.0, 1.0)
    ELSE 0 END as volume_normalized,

    CASE WHEN liquidity_usd > 0 THEN
        LEAST(LOG(liquidity_usd + 1) / 16.0, 1.0)
    ELSE 0 END as liquidity_normalized,

    -- Bonding curve features (CRITICAL for migration prediction)
    curve_percentage / 100.0 as curve_completion_normalized,

    -- Creator features (HISTORICAL PATTERNS)
    creator_success_rate / 100.0 as creator_success_rate_normalized,
    creator_reputation_score / 100.0 as creator_reputation_score_normalized,

    -- Social media presence (count of platforms)
    social_score / 8.0 as social_score_normalized,

    -- Time-based features
    token_age_hours / 720.0 as token_age_hours_normalized,
    last_activity_hours / 720.0 as last_activity_hours_normalized,

    -- Categorical features (one-hot encoded)
    CASE WHEN risk_level_encoded = 0 THEN 1 ELSE 0 END as risk_level_low,
    CASE WHEN risk_level_encoded = 1 THEN 1 ELSE 0 END as risk_level_medium,
    CASE WHEN risk_level_encoded = 2 THEN 1 ELSE 0 END as risk_level_high,
    
    CASE WHEN market_tier = 0 THEN 1 ELSE 0 END as market_tier_micro,
    CASE WHEN market_tier = 1 THEN 1 ELSE 0 END as market_tier_small,
    CASE WHEN market_tier = 2 THEN 1 ELSE 0 END as market_tier_medium,
    CASE WHEN market_tier = 3 THEN 1 ELSE 0 END as market_tier_large,

    -- TARGET VARIABLES (migration labels)
    did_migrate,
    migration_success_level
FROM token_migration_features
WHERE feature_version = 1;
