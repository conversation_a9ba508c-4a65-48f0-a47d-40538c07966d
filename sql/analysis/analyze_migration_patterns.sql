-- 🔍 Analysis of Migration Patterns for All Market Types
-- Run this after creating the migration features schema

-- 1. Complete migration distribution
SELECT 
    'MIGRATION DISTRIBUTION BY MARKET TYPE' as analysis_type,
    '' as market_type,
    0 as count,
    0.0 as percentage,
    0.0 as avg_volume
UNION ALL
SELECT 
    '',
    raw_data->'pools'->0->>'market' as market_type,
    COUNT(*) as count,
    ROUND(COUNT(*)::DECIMAL / (SELECT COUNT(*) FROM tokens) * 100, 2) as percentage,
    ROUND(AVG((raw_data->'pools'->0->'txns'->'volume')::DECIMAL), 0) as avg_volume
FROM tokens 
GROUP BY raw_data->'pools'->0->>'market'
ORDER BY count DESC;

-- 2. Migration success analysis by volume thresholds
SELECT 
    'VOLUME ANALYSIS FOR MIGRATED TOKENS' as analysis_type,
    '' as market_type,
    '' as volume_range,
    0 as token_count
UNION ALL
SELECT 
    '',
    raw_data->'pools'->0->>'market' as market_type,
    CASE 
        WHEN (raw_data->'pools'->0->'txns'->'volume')::DECIMAL >= 1000000 THEN 'High (1M+)'
        WHEN (raw_data->'pools'->0->'txns'->'volume')::DECIMAL >= 100000 THEN 'Medium (100K-1M)'
        WHEN (raw_data->'pools'->0->'txns'->'volume')::DECIMAL >= 10000 THEN 'Low (10K-100K)'
        ELSE 'Very Low (<10K)'
    END as volume_range,
    COUNT(*) as token_count
FROM tokens 
WHERE raw_data->'pools'->0->>'market' != 'pumpfun'
GROUP BY raw_data->'pools'->0->>'market', 
    CASE 
        WHEN (raw_data->'pools'->0->'txns'->'volume')::DECIMAL >= 1000000 THEN 'High (1M+)'
        WHEN (raw_data->'pools'->0->'txns'->'volume')::DECIMAL >= 100000 THEN 'Medium (100K-1M)'
        WHEN (raw_data->'pools'->0->'txns'->'volume')::DECIMAL >= 10000 THEN 'Low (10K-100K)'
        ELSE 'Very Low (<10K)'
    END
ORDER BY market_type, token_count DESC;

-- 3. Creator analysis for migrated tokens
SELECT 
    'TOP CREATORS BY MIGRATION SUCCESS' as analysis_type,
    '' as creator_address,
    0 as total_tokens,
    0 as migrated_tokens,
    0.0 as migration_rate
UNION ALL
SELECT 
    '',
    creator_address,
    COUNT(*) as total_tokens,
    COUNT(CASE WHEN raw_data->'pools'->0->>'market' != 'pumpfun' THEN 1 END) as migrated_tokens,
    ROUND(COUNT(CASE WHEN raw_data->'pools'->0->>'market' != 'pumpfun' THEN 1 END)::DECIMAL / COUNT(*) * 100, 1) as migration_rate
FROM tokens 
WHERE creator_address IS NOT NULL
GROUP BY creator_address
HAVING COUNT(*) >= 10 -- At least 10 tokens
   AND COUNT(CASE WHEN raw_data->'pools'->0->>'market' != 'pumpfun' THEN 1 END) > 0
ORDER BY migration_rate DESC, migrated_tokens DESC
LIMIT 20;

-- 4. Bonding curve completion analysis
SELECT 
    'BONDING CURVE COMPLETION ANALYSIS' as analysis_type,
    '' as curve_status,
    0 as count,
    0.0 as avg_volume
UNION ALL
SELECT 
    '',
    CASE 
        WHEN raw_data->'pools'->0->>'curve' IS NULL THEN 'No Curve (Migrated)'
        WHEN (raw_data->'pools'->0->'curvePercentage')::DECIMAL >= 100 THEN 'Complete (100%)'
        WHEN (raw_data->'pools'->0->'curvePercentage')::DECIMAL >= 90 THEN 'Near Complete (90-99%)'
        WHEN (raw_data->'pools'->0->'curvePercentage')::DECIMAL >= 50 THEN 'Halfway (50-89%)'
        ELSE 'Early Stage (<50%)'
    END as curve_status,
    COUNT(*) as count,
    ROUND(AVG((raw_data->'pools'->0->'txns'->'volume')::DECIMAL), 0) as avg_volume
FROM tokens
GROUP BY 
    CASE 
        WHEN raw_data->'pools'->0->>'curve' IS NULL THEN 'No Curve (Migrated)'
        WHEN (raw_data->'pools'->0->'curvePercentage')::DECIMAL >= 100 THEN 'Complete (100%)'
        WHEN (raw_data->'pools'->0->'curvePercentage')::DECIMAL >= 90 THEN 'Near Complete (90-99%)'
        WHEN (raw_data->'pools'->0->'curvePercentage')::DECIMAL >= 50 THEN 'Halfway (50-89%)'
        ELSE 'Early Stage (<50%)'
    END
ORDER BY count DESC;

-- 5. Social media correlation with migration
SELECT 
    'SOCIAL MEDIA IMPACT ON MIGRATION' as analysis_type,
    '' as social_presence,
    0 as total_tokens,
    0 as migrated_tokens,
    0.0 as migration_rate
UNION ALL
SELECT 
    '',
    CASE 
        WHEN (raw_data->'token'->>'twitter' IS NOT NULL AND raw_data->'token'->>'twitter' != '') 
             AND (raw_data->'token'->>'website' IS NOT NULL AND raw_data->'token'->>'website' != '')
             AND (raw_data->'token'->>'telegram' IS NOT NULL AND raw_data->'token'->>'telegram' != '')
        THEN 'Full Social (Twitter+Website+Telegram)'
        WHEN (raw_data->'token'->>'twitter' IS NOT NULL AND raw_data->'token'->>'twitter' != '') 
             AND (raw_data->'token'->>'website' IS NOT NULL AND raw_data->'token'->>'website' != '')
        THEN 'Twitter + Website'
        WHEN (raw_data->'token'->>'twitter' IS NOT NULL AND raw_data->'token'->>'twitter' != '')
        THEN 'Twitter Only'
        WHEN (raw_data->'token'->>'website' IS NOT NULL AND raw_data->'token'->>'website' != '')
        THEN 'Website Only'
        ELSE 'No Social Media'
    END as social_presence,
    COUNT(*) as total_tokens,
    COUNT(CASE WHEN raw_data->'pools'->0->>'market' != 'pumpfun' THEN 1 END) as migrated_tokens,
    ROUND(COUNT(CASE WHEN raw_data->'pools'->0->>'market' != 'pumpfun' THEN 1 END)::DECIMAL / COUNT(*) * 100, 2) as migration_rate
FROM tokens
GROUP BY 
    CASE 
        WHEN (raw_data->'token'->>'twitter' IS NOT NULL AND raw_data->'token'->>'twitter' != '') 
             AND (raw_data->'token'->>'website' IS NOT NULL AND raw_data->'token'->>'website' != '')
             AND (raw_data->'token'->>'telegram' IS NOT NULL AND raw_data->'token'->>'telegram' != '')
        THEN 'Full Social (Twitter+Website+Telegram)'
        WHEN (raw_data->'token'->>'twitter' IS NOT NULL AND raw_data->'token'->>'twitter' != '') 
             AND (raw_data->'token'->>'website' IS NOT NULL AND raw_data->'token'->>'website' != '')
        THEN 'Twitter + Website'
        WHEN (raw_data->'token'->>'twitter' IS NOT NULL AND raw_data->'token'->>'twitter' != '')
        THEN 'Twitter Only'
        WHEN (raw_data->'token'->>'website' IS NOT NULL AND raw_data->'token'->>'website' != '')
        THEN 'Website Only'
        ELSE 'No Social Media'
    END
ORDER BY migration_rate DESC;

-- 6. Risk score correlation
SELECT 
    'RISK SCORE CORRELATION WITH MIGRATION' as analysis_type,
    0 as risk_score_range,
    0 as total_tokens,
    0 as migrated_tokens,
    0.0 as migration_rate
UNION ALL
SELECT 
    '',
    CASE 
        WHEN (raw_data->'risk'->>'score')::INTEGER <= 2 THEN 0 -- Low risk
        WHEN (raw_data->'risk'->>'score')::INTEGER <= 5 THEN 1 -- Medium risk
        ELSE 2 -- High risk
    END as risk_score_range,
    COUNT(*) as total_tokens,
    COUNT(CASE WHEN raw_data->'pools'->0->>'market' != 'pumpfun' THEN 1 END) as migrated_tokens,
    ROUND(COUNT(CASE WHEN raw_data->'pools'->0->>'market' != 'pumpfun' THEN 1 END)::DECIMAL / COUNT(*) * 100, 2) as migration_rate
FROM tokens
WHERE raw_data->'risk'->>'score' IS NOT NULL
GROUP BY 
    CASE 
        WHEN (raw_data->'risk'->>'score')::INTEGER <= 2 THEN 0
        WHEN (raw_data->'risk'->>'score')::INTEGER <= 5 THEN 1
        ELSE 2
    END
ORDER BY risk_score_range;

-- 7. Summary statistics
SELECT 
    'SUMMARY STATISTICS' as metric,
    '' as value
UNION ALL
SELECT 'Total Tokens', COUNT(*)::TEXT FROM tokens
UNION ALL
SELECT 'Migrated Tokens', COUNT(CASE WHEN raw_data->'pools'->0->>'market' != 'pumpfun' THEN 1 END)::TEXT FROM tokens
UNION ALL
SELECT 'Migration Rate %', ROUND(COUNT(CASE WHEN raw_data->'pools'->0->>'market' != 'pumpfun' THEN 1 END)::DECIMAL / COUNT(*) * 100, 2)::TEXT FROM tokens
UNION ALL
SELECT 'Unique Creators', COUNT(DISTINCT creator_address)::TEXT FROM tokens WHERE creator_address IS NOT NULL
UNION ALL
SELECT 'Avg Volume (Migrated)', ROUND(AVG((raw_data->'pools'->0->'txns'->'volume')::DECIMAL), 0)::TEXT FROM tokens WHERE raw_data->'pools'->0->>'market' != 'pumpfun'
UNION ALL
SELECT 'Avg Volume (Non-Migrated)', ROUND(AVG((raw_data->'pools'->0->'txns'->'volume')::DECIMAL), 0)::TEXT FROM tokens WHERE raw_data->'pools'->0->>'market' = 'pumpfun';
