-- 🚀 SolanaTracker API Integration Functions
-- Functions to fetch and process trading data from SolanaTracker API

-- Function to fetch trades for a token and update the database
CREATE OR REPLACE FUNCTION fetch_and_store_token_trades(
    p_token_address VARCHAR(44),
    p_api_key VARCHAR(100),
    p_limit INTEGER DEFAULT 100,
    p_sort_direction VARCHAR(10) DEFAULT 'desc'
) RETURNS TABLE(
    trades_fetched INTEGER,
    trades_inserted INTEGER,
    api_response_status VARCHAR(20)
) AS $$
DECLARE
    api_url TEXT;
    curl_command TEXT;
    api_response TEXT;
    response_json JSONB;
    trades_count INTEGER := 0;
    inserted_count INTEGER := 0;
BEGIN
    -- Build API URL
    api_url := 'https://data.solanatracker.io/trades/' || p_token_address || 
               '?limit=' || p_limit || '&sortDirection=' || p_sort_direction;
    
    -- Build curl command
    curl_command := 'curl -s -X GET "' || api_url || '" ' ||
                   '-H "x-api-key: ' || p_api_key || '" ' ||
                   '-H "Content-Type: application/json"';
    
    -- Note: This is a placeholder - actual implementation would need
    -- to use a proper HTTP client or external function
    -- For now, we'll assume the JSON data is passed directly
    
    RETURN QUERY SELECT 0, 0, 'placeholder'::VARCHAR(20);
END;
$$ LANGUAGE plpgsql;

-- Function to analyze trading patterns for migration prediction
CREATE OR REPLACE FUNCTION analyze_trading_patterns(p_token_address VARCHAR(44))
RETURNS TABLE(
    migration_probability DECIMAL(5,4),
    key_indicators JSONB,
    risk_factors JSONB,
    recommendation VARCHAR(50)
) AS $$
DECLARE
    trading_stats RECORD;
    migration_prob DECIMAL(5,4) := 0;
    indicators JSONB := '{}';
    risks JSONB := '{}';
    recommendation_text VARCHAR(50) := 'HOLD';
    
    -- Thresholds for migration indicators
    MIN_VOLUME_THRESHOLD DECIMAL := 50000; -- $50k minimum volume
    MIN_UNIQUE_TRADERS INTEGER := 50;
    MIN_CURVE_COMPLETION DECIMAL := 80.0; -- 80% curve completion
    MAX_WHALE_DOMINANCE DECIMAL := 0.7; -- Max 70% whale trades
BEGIN
    -- Get comprehensive trading statistics
    SELECT 
        tmf.total_trade_volume_usd,
        tmf.unique_buyers + tmf.unique_sellers as total_unique_traders,
        tmf.curve_percentage,
        tmf.whale_trades_count,
        tmf.retail_trades_count,
        tmf.buy_sell_ratio,
        tmf.momentum_score,
        tmf.creator_success_rate,
        tmf.social_score,
        tmf.volume_last_24h,
        tmf.trading_velocity
    INTO trading_stats
    FROM token_migration_features tmf
    WHERE tmf.token_address = p_token_address;
    
    -- Calculate migration probability based on key factors
    migration_prob := 0;
    
    -- Volume factor (30% weight)
    IF trading_stats.total_trade_volume_usd >= MIN_VOLUME_THRESHOLD THEN
        migration_prob := migration_prob + 0.30;
        indicators := indicators || jsonb_build_object('volume_sufficient', true);
    ELSE
        risks := risks || jsonb_build_object('low_volume', trading_stats.total_trade_volume_usd);
    END IF;
    
    -- Trader diversity factor (20% weight)
    IF trading_stats.total_unique_traders >= MIN_UNIQUE_TRADERS THEN
        migration_prob := migration_prob + 0.20;
        indicators := indicators || jsonb_build_object('trader_diversity_good', true);
    ELSE
        risks := risks || jsonb_build_object('low_trader_count', trading_stats.total_unique_traders);
    END IF;
    
    -- Bonding curve completion (25% weight)
    IF trading_stats.curve_percentage >= MIN_CURVE_COMPLETION THEN
        migration_prob := migration_prob + 0.25;
        indicators := indicators || jsonb_build_object('curve_near_completion', true);
    ELSE
        risks := risks || jsonb_build_object('curve_completion_low', trading_stats.curve_percentage);
    END IF;
    
    -- Whale dominance check (negative factor)
    IF trading_stats.whale_trades_count + trading_stats.retail_trades_count > 0 THEN
        DECLARE
            whale_ratio DECIMAL := trading_stats.whale_trades_count::DECIMAL / 
                                 (trading_stats.whale_trades_count + trading_stats.retail_trades_count);
        BEGIN
            IF whale_ratio <= MAX_WHALE_DOMINANCE THEN
                migration_prob := migration_prob + 0.15;
                indicators := indicators || jsonb_build_object('healthy_distribution', true);
            ELSE
                risks := risks || jsonb_build_object('whale_dominance', whale_ratio);
                migration_prob := migration_prob - 0.10; -- Penalty for whale dominance
            END IF;
        END;
    END IF;
    
    -- Creator track record (10% weight)
    IF trading_stats.creator_success_rate > 0.5 THEN
        migration_prob := migration_prob + 0.10;
        indicators := indicators || jsonb_build_object('creator_experienced', true);
    END IF;
    
    -- Momentum and recent activity
    IF trading_stats.momentum_score > 1.5 AND trading_stats.volume_last_24h > 1000 THEN
        migration_prob := migration_prob + 0.05;
        indicators := indicators || jsonb_build_object('strong_momentum', true);
    END IF;
    
    -- Cap probability at 1.0
    migration_prob := LEAST(migration_prob, 1.0);
    
    -- Generate recommendation
    IF migration_prob >= 0.8 THEN
        recommendation_text := 'STRONG_BUY';
    ELSIF migration_prob >= 0.6 THEN
        recommendation_text := 'BUY';
    ELSIF migration_prob >= 0.4 THEN
        recommendation_text := 'HOLD';
    ELSIF migration_prob >= 0.2 THEN
        recommendation_text := 'CAUTION';
    ELSE
        recommendation_text := 'AVOID';
    END IF;
    
    RETURN QUERY SELECT 
        migration_prob,
        indicators,
        risks,
        recommendation_text;
END;
$$ LANGUAGE plpgsql;

-- Function to get top migration candidates
CREATE OR REPLACE FUNCTION get_migration_candidates(p_limit INTEGER DEFAULT 20)
RETURNS TABLE(
    token_address VARCHAR(44),
    token_name VARCHAR(100),
    migration_probability DECIMAL(5,4),
    volume_24h DECIMAL(20,2),
    curve_completion DECIMAL(5,2),
    unique_traders INTEGER,
    recommendation VARCHAR(50)
) AS $$
BEGIN
    RETURN QUERY
    WITH candidate_analysis AS (
        SELECT 
            tmf.token_address,
            tmf.token_name,
            tmf.volume_last_24h,
            tmf.curve_percentage,
            tmf.unique_buyers + tmf.unique_sellers as traders,
            ap.*
        FROM token_migration_features tmf
        CROSS JOIN LATERAL analyze_trading_patterns(tmf.token_address) ap
        WHERE tmf.did_migrate = FALSE -- Only non-migrated tokens
        AND tmf.total_trade_volume_usd > 1000 -- Minimum volume filter
        AND tmf.curve_percentage > 50 -- At least 50% curve completion
    )
    SELECT 
        ca.token_address,
        ca.token_name,
        ca.migration_probability,
        ca.volume_last_24h,
        ca.curve_percentage,
        ca.traders,
        ca.recommendation
    FROM candidate_analysis ca
    ORDER BY ca.migration_probability DESC, ca.volume_last_24h DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Function to update trading features for all tokens
CREATE OR REPLACE FUNCTION refresh_all_trading_features()
RETURNS INTEGER AS $$
DECLARE
    token_record RECORD;
    updated_count INTEGER := 0;
BEGIN
    FOR token_record IN 
        SELECT DISTINCT token_address 
        FROM token_trades 
        WHERE created_at >= NOW() - INTERVAL '7 days'
    LOOP
        -- Update migration features for this token
        PERFORM extract_migration_features(
            token_record.token_address,
            NULL, -- creator_address will be fetched from tokens table if needed
            '{}'::JSONB, -- raw_data placeholder
            NOW(),
            NOW()
        );
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;
