-- Setup script for SES PostgreSQL database
-- Run this script as postgres superuser

-- Create database
CREATE DATABASE ses_db;

-- Connect to the database
\c ses_db;

-- Create user (optional, you can use postgres user)
-- CREATE USER ses_user WITH PASSWORD 'ses_password';
-- GRANT ALL PRIVILEGES ON DATABASE ses_db TO ses_user;

-- The application will create the tables automatically via migrations
-- But you can run this manually if needed:

/*
CREATE TABLE IF NOT EXISTS tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_address VARCHAR(44) NOT NULL UNIQUE,
    creator_address VARCHAR(44) NOT NULL,
    raw_data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tokens_token_address ON tokens(token_address);
CREATE INDEX IF NOT EXISTS idx_tokens_creator_address ON tokens(creator_address);
CREATE INDEX IF NOT EXISTS idx_tokens_created_at ON tokens(created_at);

-- Create GIN index for JSONB queries
CREATE INDEX IF NOT EXISTS idx_tokens_raw_data_gin ON tokens USING GIN(raw_data);
*/
