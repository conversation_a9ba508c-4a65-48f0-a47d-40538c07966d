[package]
name = "ses"
version = "1.0.0"
edition = "2024"
description = "Solana Enhanced Scanner - High-performance token data collection and analysis"
authors = ["SES Team"]
license = "MIT"
repository = "https://github.com/your-org/ses"
keywords = ["solana", "blockchain", "token", "analysis", "scanner"]
categories = ["cryptocurrency", "web-programming", "database"]

[dependencies]
# Web framework
axum = "0.8.4"
tokio = { version = "1.45.1", features = ["full"] }
tower = "0.5.2"
tower-http = { version = "0.6.6", features = ["cors", "trace"] }

# HTTP client
reqwest = { version = "0.12.2", features = ["json"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Environment variables
dotenvy = "0.15"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling
anyhow = "1.0"
thiserror = "2.0.12"
# Date/time
chrono = { version = "0.4", features = ["serde"] }

# Database
sqlx = { version = "0.8", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid", "json"] }
tokio-postgres = { version = "0.7", features = ["with-chrono-0_4", "with-serde_json-1"] }
rust_decimal = { version = "1.36", features = ["tokio-pg"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
