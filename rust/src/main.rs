mod client;
mod database;
mod handlers;
mod helius_client;
mod token_processor;

use anyhow::{Context, Result};
use axum::{
    routing::{get, post},
    Router,
};
use std::env;
use tower::ServiceBuilder;
use tower_http::{cors::Cors<PERSON>ayer, trace::TraceLayer};
use tracing::{info, error};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use client::SolanaTrackerClient;
use database::Database;
use handlers::{collect_graduated_tokens, health_check, get_status};
use helius_client::HeliusClient;

#[derive(Clone)]
pub struct AppState {
    pub client: SolanaTrackerClient,
    pub database: Database,
    pub helius_client: HeliusClient,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "ses=info,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // Load environment variables
    dotenvy::dotenv().ok();

    info!("Starting SES Backend server...");

    // Get configuration from environment
    let api_key = env::var("SOLANA_TRACKER_API_KEY")
        .context("SOLANA_TRACKER_API_KEY environment variable is required")?;

    let base_url = env::var("SOLANA_TRACKER_BASE_URL")
        .unwrap_or_else(|_| "https://data.solanatracker.io".to_string());

    let host = env::var("SERVER_HOST")
        .unwrap_or_else(|_| "127.0.0.1".to_string());

    let port = env::var("SERVER_PORT")
        .unwrap_or_else(|_| "3001".to_string())
        .parse::<u16>()
        .context("Invalid SERVER_PORT value")?;

    let database_url = env::var("DATABASE_URL")
        .context("DATABASE_URL environment variable is required")?;

    let helius_api_key = env::var("HELIUS_API_KEY")
        .context("HELIUS_API_KEY environment variable is required")?;

    let helius_base_url = env::var("HELIUS_BASE_URL")
        .unwrap_or_else(|_| "https://mainnet.helius-rpc.com".to_string());

    // Initialize components
    let client = SolanaTrackerClient::new(api_key, base_url)
        .context("Failed to create Solana Tracker client")?;

    let database = Database::new(&database_url)
        .await
        .context("Failed to create database connection")?;

    let helius_client = HeliusClient::new(helius_api_key, helius_base_url)
        .context("Failed to create Helius client")?;

    // Create application state
    let app_state = AppState {
        client,
        database,
        helius_client,
    };

    // Build the application router
    let app = Router::new()
        .route("/collect", post(collect_graduated_tokens))
        .route("/health", get(health_check))
        .route("/status", get(get_status))
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(CorsLayer::permissive()),
        )
        .with_state(app_state);

    // Start the server
    let listener = tokio::net::TcpListener::bind(format!("{}:{}", host, port))
        .await
        .context("Failed to bind to address")?;

    info!("Server listening on http://{}:{}", host, port);
    info!("Available endpoints:");
    info!("  POST /collect - Complete token collection and processing workflow");
    info!("  GET  /health  - Health check");
    info!("  GET  /status  - Service status");

    if let Err(e) = axum::serve(listener, app).await {
        error!("Server error: {}", e);
        return Err(e.into());
    }

    Ok(())
}
