use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::time::Duration;
use tokio::sync::mpsc;
use tokio::time::{interval, sleep};
use tracing::{info, warn, error, debug};

use crate::client::SolanaTrackerClient;
use crate::database::Database;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TokenBatch {
    pub tokens: Vec<String>,
    pub batch_id: usize,
    pub retry_count: usize,
    pub last_error: Option<String>,
}

#[derive(Debug, <PERSON>lone)]
pub enum RetryStrategy {
    Immediate,
    ExponentialBackoff { base_delay_ms: u64, max_delay_ms: u64 },
    Skip,
}



#[derive(Debug, Serialize, Deserialize)]
pub struct BatchProcessingStats {
    pub total_tokens_extracted: usize,
    pub total_batches: usize,
    pub batches_processed: usize,
    pub batches_failed: usize,
    pub tokens_processed: usize,
    pub tokens_failed: usize,
    pub tokens_saved_to_db: usize,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    pub duration_seconds: Option<f64>,
}

pub struct TokenProcessor {
    solana_client: SolanaTrackerClient,
    database: Database,
}

impl TokenProcessor {
    pub fn new(solana_client: SolanaTrackerClient, database: Database) -> Self {
        Self {
            solana_client,
            database,
        }
    }

    /// Determine retry strategy based on error type
    fn determine_retry_strategy(&self, error: &anyhow::Error) -> RetryStrategy {
        let error_msg = error.to_string().to_lowercase();

        // Rate limiting errors - use exponential backoff
        if error_msg.contains("rate limit") || error_msg.contains("429") || error_msg.contains("too many requests") {
            return RetryStrategy::ExponentialBackoff {
                base_delay_ms: 2000,
                max_delay_ms: 30000
            };
        }

        // Timeout or connection errors - immediate retry
        if error_msg.contains("timeout") || error_msg.contains("connection") || error_msg.contains("network") {
            return RetryStrategy::Immediate;
        }

        // Server errors (5xx) - exponential backoff
        if error_msg.contains("500") || error_msg.contains("502") || error_msg.contains("503") || error_msg.contains("504") {
            return RetryStrategy::ExponentialBackoff {
                base_delay_ms: 1000,
                max_delay_ms: 15000
            };
        }

        // Client errors (4xx except 429) - skip retry
        if error_msg.contains("400") || error_msg.contains("401") || error_msg.contains("403") || error_msg.contains("404") {
            return RetryStrategy::Skip;
        }

        // Default: exponential backoff for unknown errors
        RetryStrategy::ExponentialBackoff {
            base_delay_ms: 1000,
            max_delay_ms: 10000
        }
    }

    /// Calculate delay for exponential backoff
    fn calculate_backoff_delay(&self, retry_count: usize, base_delay_ms: u64, max_delay_ms: u64) -> Duration {
        let delay_ms = std::cmp::min(
            base_delay_ms * (2_u64.pow(retry_count as u32)),
            max_delay_ms
        );
        Duration::from_millis(delay_ms)
    }



    /// Validate token data structure and required fields
    fn validate_token_data(&self, token_address: &str, token_data: &Value) -> Result<()> {
        // Check if token object exists
        let token_obj = token_data.get("token")
            .ok_or_else(|| anyhow::anyhow!("Missing 'token' object"))?;

        // Validate mint address
        let mint_address = token_obj.get("mint")
            .and_then(|m| m.as_str())
            .ok_or_else(|| anyhow::anyhow!("Missing or invalid 'mint' address"))?;

        // Ensure mint address matches the key
        if mint_address != token_address {
            return Err(anyhow::anyhow!("Mint address mismatch: key={}, mint={}", token_address, mint_address));
        }

        // Note: We don't validate 'creation' field because:
        // 1. Tokens come from Helius API which already filters for pump.fun tokens
        // 2. Some tokens legitimately don't have creation field or creator
        // 3. Database handles NULL creator addresses correctly
        // 4. We already filtered for pump.fun tokens in the initial collection phase
        info!("Token {} validation passed (pump.fun token from Helius)", token_address);

        Ok(())
    }

    /// Extract token addresses from Helius assets response (without saving assets)
    pub fn extract_token_addresses_from_assets(&self, assets: &[Value]) -> Vec<String> {
        let mut token_addresses = Vec::new();

        for asset in assets {
            if let Some(asset_id) = asset.get("id").and_then(|id| id.as_str()) {
                token_addresses.push(asset_id.to_string());
            }
        }

        info!("Extracted {} token addresses from {} assets", token_addresses.len(), assets.len());
        token_addresses
    }



    /// Create batches of token addresses from a list (max 20 per batch)
    pub fn create_token_batches_from_list(&self, token_addresses: Vec<String>) -> Vec<TokenBatch> {
        let batches: Vec<TokenBatch> = token_addresses
            .chunks(20)
            .enumerate()
            .map(|(index, chunk)| TokenBatch {
                tokens: chunk.to_vec(),
                batch_id: index,
                retry_count: 0,
                last_error: None,
            })
            .collect();

        info!(
            "Created {} batches from {} unique tokens",
            batches.len(),
            token_addresses.len()
        );

        batches
    }

    /// Process token addresses with rate limiting and retry logic
    pub async fn process_token_addresses(&self, token_addresses: Vec<String>) -> Result<BatchProcessingStats> {
        let start_time = chrono::Utc::now();
        debug!("🔍 DEBUG: Starting process_token_addresses with {} addresses", token_addresses.len());
        debug!("🔍 DEBUG: First 5 token addresses: {:?}", &token_addresses[..std::cmp::min(5, token_addresses.len())]);

        let batches = self.create_token_batches_from_list(token_addresses);
        let total_tokens = batches.iter().map(|b| b.tokens.len()).sum();
        let total_batches = batches.len();

        info!(
            "Starting batch processing: {} batches, {} tokens total",
            total_batches, total_tokens
        );
        debug!("🔍 DEBUG: Created {} batches, first batch has {} tokens", total_batches, batches.get(0).map(|b| b.tokens.len()).unwrap_or(0));

        // Create streaming queue for batches with sufficient capacity for large datasets
        let queue_capacity = std::cmp::max(1000000, total_batches * 2); // At least 1000 or 2x batch count
        let (tx, mut rx) = mpsc::channel::<TokenBatch>(queue_capacity);
        debug!("🔍 DEBUG: Created streaming queue with capacity {} for {} batches", queue_capacity, total_batches);

        // Send all batches to the queue
        for (index, batch) in batches.into_iter().enumerate() {
            debug!("🔍 DEBUG: Sending batch {} to queue (batch_id: {}, tokens: {})", index, batch.batch_id, batch.tokens.len());
            if let Err(e) = tx.send(batch).await {
                error!("Failed to send batch to queue: {}", e);
            }
        }
        drop(tx); // Close the sender
        debug!("🔍 DEBUG: All batches sent to queue, sender closed");

        // Process batches with rate limiting (2 seconds per request to avoid 429 errors)
        let mut interval = interval(Duration::from_secs(2));
        let mut batches_processed = 0;
        let mut batches_failed = 0;
        let mut tokens_processed = 0;
        let mut tokens_failed = 0;
        let mut tokens_saved_to_db = 0;
        let mut failed_batches: Vec<(TokenBatch, RetryStrategy)> = Vec::new();

        debug!("🔍 DEBUG: Starting main processing loop");
        while let Some(batch) = rx.recv().await {
            debug!("🔍 DEBUG: Received batch {} from queue", batch.batch_id);

            // Wait for rate limit
            debug!("🔍 DEBUG: Waiting for rate limit interval...");
            interval.tick().await;
            debug!("🔍 DEBUG: Rate limit interval completed, proceeding with batch {}", batch.batch_id);

            info!(
                "Processing batch {} with {} tokens",
                batch.batch_id,
                batch.tokens.len()
            );
            debug!("🔍 DEBUG: Batch {} tokens: {:?}", batch.batch_id, &batch.tokens[..std::cmp::min(3, batch.tokens.len())]);

            debug!("🔍 DEBUG: Calling process_single_batch for batch {}", batch.batch_id);
            match self.process_single_batch(&batch).await {
                Ok(response) => {
                    debug!("🔍 DEBUG: process_single_batch succeeded for batch {}", batch.batch_id);
                    batches_processed += 1;
                    tokens_processed += batch.tokens.len();

                    debug!("🔍 DEBUG: Calling save_tokens_to_database for batch {}", batch.batch_id);
                    // Count tokens saved to database
                    if let Ok(saved_count) = self.save_tokens_to_database(&response).await {
                        tokens_saved_to_db += saved_count;
                        debug!("🔍 DEBUG: Saved {} tokens to database for batch {}", saved_count, batch.batch_id);
                    } else {
                        debug!("🔍 DEBUG: Failed to save tokens to database for batch {}", batch.batch_id);
                    }

                    info!("Successfully processed batch {}", batch.batch_id);
                }
                Err(e) => {
                    error!("Failed to process batch {}: {}", batch.batch_id, e);

                    let retry_strategy = self.determine_retry_strategy(&e);

                    match retry_strategy {
                        RetryStrategy::Skip => {
                            error!("Skipping batch {} due to non-retryable error: {}", batch.batch_id, e);
                            batches_failed += 1;
                            tokens_failed += batch.tokens.len();
                        }
                        _ if batch.retry_count < 3 => {
                            // Retry failed batch with strategy
                            let mut retry_batch = batch.clone();
                            retry_batch.retry_count += 1;
                            retry_batch.last_error = Some(e.to_string());
                            let retry_count = retry_batch.retry_count;
                            failed_batches.push((retry_batch, retry_strategy.clone()));
                            warn!("Batch {} will be retried (attempt {}) with strategy: {:?}", batch.batch_id, retry_count, retry_strategy);
                        }
                        _ => {
                            batches_failed += 1;
                            tokens_failed += batch.tokens.len();
                            error!("Batch {} failed after 3 retries, last error: {}", batch.batch_id, e);
                        }
                    }
                }
            }
        }

        // Process failed batches with intelligent retry strategies
        for (failed_batch, retry_strategy) in failed_batches {
            // Apply retry strategy delay
            match retry_strategy {
                RetryStrategy::Immediate => {
                    // No additional delay beyond rate limiting
                    interval.tick().await;
                }
                RetryStrategy::ExponentialBackoff { base_delay_ms, max_delay_ms } => {
                    let backoff_delay = self.calculate_backoff_delay(
                        failed_batch.retry_count,
                        base_delay_ms,
                        max_delay_ms
                    );
                    info!("Applying exponential backoff delay: {:?} for batch {}", backoff_delay, failed_batch.batch_id);
                    sleep(backoff_delay).await;
                    interval.tick().await; // Still respect rate limiting
                }
                RetryStrategy::Skip => {
                    // This should not happen as Skip batches are not added to retry list
                    continue;
                }
            }

            info!(
                "Retrying batch {} (attempt {}) with strategy: {:?}",
                failed_batch.batch_id,
                failed_batch.retry_count,
                retry_strategy
            );

            match self.process_single_batch(&failed_batch).await {
                Ok(response) => {
                    batches_processed += 1;
                    tokens_processed += failed_batch.tokens.len();

                    // Count tokens saved to database
                    if let Ok(saved_count) = self.save_tokens_to_database(&response).await {
                        tokens_saved_to_db += saved_count;
                    }

                    info!("Successfully processed batch {} on retry", failed_batch.batch_id);
                }
                Err(e) => {
                    error!("Retry failed for batch {}: {}", failed_batch.batch_id, e);
                    batches_failed += 1;
                    tokens_failed += failed_batch.tokens.len();
                }
            }
        }

        let end_time = chrono::Utc::now();
        let duration = end_time.signed_duration_since(start_time);

        let stats = BatchProcessingStats {
            total_tokens_extracted: total_tokens,
            total_batches,
            batches_processed,
            batches_failed,
            tokens_processed,
            tokens_failed,
            tokens_saved_to_db,
            start_time,
            end_time: Some(end_time),
            duration_seconds: Some(duration.num_seconds() as f64),
        };

        // Calculate success rates
        let batch_success_rate = if total_batches > 0 {
            (batches_processed as f64 / total_batches as f64) * 100.0
        } else {
            0.0
        };

        let token_success_rate = if total_tokens > 0 {
            (tokens_processed as f64 / total_tokens as f64) * 100.0
        } else {
            0.0
        };

        info!(
            "🎯 BATCH PROCESSING COMPLETED: {}/{} batches successful ({:.1}%), {}/{} tokens processed ({:.1}%), {} saved to database, duration: {}s",
            batches_processed,
            total_batches,
            batch_success_rate,
            tokens_processed,
            total_tokens,
            token_success_rate,
            tokens_saved_to_db,
            duration.num_seconds()
        );

        if batches_failed > 0 {
            warn!("⚠️  {} batches failed after all retries, {} tokens lost", batches_failed, tokens_failed);
        }

        Ok(stats)
    }

    /// Process a single batch of tokens
    async fn process_single_batch(&self, batch: &TokenBatch) -> Result<Value> {
        debug!("🔍 DEBUG: process_single_batch starting for batch {} with {} tokens", batch.batch_id, batch.tokens.len());

        let request_body = serde_json::json!({
            "tokens": batch.tokens
        });
        debug!("🔍 DEBUG: Created request body for batch {}", batch.batch_id);

        // Call Solana Tracker API
        debug!("🔍 DEBUG: Calling Solana Tracker API for batch {}", batch.batch_id);
        let response = self.solana_client.post_tokens_multi(&request_body).await
            .context("Failed to call Solana Tracker /tokens/multi API")?;
        debug!("🔍 DEBUG: Solana Tracker API call completed for batch {}", batch.batch_id);

        info!(
            "Batch {} API call successful: {} tokens fetched",
            batch.batch_id,
            batch.tokens.len()
        );

        Ok(response)
    }

    /// Save tokens from API response to database
    async fn save_tokens_to_database(&self, api_response: &Value) -> Result<usize> {
        debug!("🔍 DEBUG: save_tokens_to_database starting");

        // The API response has structure: {"tokens": {"address1": {...}, "address2": {...}}}
        debug!("🔍 DEBUG: Extracting tokens object from API response");
        let tokens_object = api_response.get("tokens")
            .and_then(|t| t.as_object())
            .ok_or_else(|| anyhow::anyhow!("API response missing 'tokens' object"))?;

        info!("🔍 Processing {} tokens from API response for database insertion", tokens_object.len());
        debug!("🔍 DEBUG: tokens_object extracted successfully with {} entries", tokens_object.len());

        if tokens_object.is_empty() {
            warn!("⚠️  API response contains no tokens to process");
            return Ok(0);
        }

        // Filter and prepare tokens for database insertion
        let mut valid_tokens = Vec::new();
        let mut skipped_tokens = 0;

        debug!("🔍 DEBUG: Starting token validation loop");
        for (index, (token_address, token_data)) in tokens_object.iter().enumerate() {
            debug!("🔍 DEBUG: Processing token {} of {} (address: {})", index + 1, tokens_object.len(), token_address);

            // Log the structure of the first few tokens for debugging
            if index < 3 {
                info!("📋 Token {} structure: {}", token_address, serde_json::to_string_pretty(token_data).unwrap_or_else(|_| "Invalid JSON".to_string()));
            }

            // Validate token structure
            debug!("🔍 DEBUG: Validating token data for {}", token_address);
            match self.validate_token_data(token_address, token_data) {
                Ok(()) => {
                    valid_tokens.push(token_data.clone());
                    if index < 5 { // Log first 5 tokens for debugging
                        info!("✅ Prepared token {} for database insertion", token_address);
                    }
                }
                Err(e) => {
                    skipped_tokens += 1;
                    if skipped_tokens <= 5 { // Log first 5 errors for debugging
                        warn!("❌ Skipping token {}: {}", token_address, e);
                    }
                }
            }
        }

        if skipped_tokens > 5 {
            warn!("⚠️  Skipped {} additional tokens due to validation errors", skipped_tokens - 5);
        }

        info!("📊 Validation results: {}/{} tokens valid, {} skipped",
              valid_tokens.len(), tokens_object.len(), skipped_tokens);

        if valid_tokens.is_empty() {
            info!("No valid tokens to save to database");
            return Ok(0);
        }

        // Save to database using existing method
        debug!("🔍 DEBUG: Calling database.save_tokens with {} valid tokens", valid_tokens.len());
        let saved_count = self.database.save_tokens(&valid_tokens).await
            .context("Failed to save tokens to database")?;
        debug!("🔍 DEBUG: database.save_tokens completed, saved {} tokens", saved_count);

        info!("Saved {} tokens to database from API response", saved_count);
        debug!("🔍 DEBUG: save_tokens_to_database completed successfully");
        Ok(saved_count)
    }


}
