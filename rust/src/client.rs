use anyhow::{Context, Result};
use reqwest::Client;
use std::time::Duration;
use tracing::{info, warn, error};

use serde_json::Value;

#[derive(Clone)]
pub struct SolanaTrackerClient {
    client: Client,
    api_key: String,
    base_url: String,
}

impl SolanaTrackerClient {
    pub fn new(api_key: String, base_url: String) -> Result<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(120)) // Increased from 30 to 120 seconds for batch processing
            .connect_timeout(Duration::from_secs(30))
            .user_agent("SES-Backend/1.0")
            .build()
            .context("Failed to create HTTP client")?;

        Ok(Self {
            client,
            api_key,
            base_url,
        })
    }

    pub async fn get_graduated_tokens(&self) -> Result<Value> {
        let url = format!("{}/tokens/multi/graduated", self.base_url);
        
        info!("Fetching graduated tokens from: {}", url);

        let response = self
            .client
            .get(&url)
            .header("x-api-key", &self.api_key)
            .header("Accept", "application/json")
            .send()
            .await
            .context("Failed to send request to Solana Tracker API")?;

        let status = response.status();
        info!("API response status: {}", status);

        if !status.is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            
            error!("API request failed with status {}: {}", status, error_text);
            
            return Err(anyhow::anyhow!(
                "API request failed with status {}: {}", 
                status, 
                error_text
            ));
        }

        let response_text = response
            .text()
            .await
            .context("Failed to read response body")?;

        // Log first 500 characters of response for debugging
        let preview = if response_text.len() > 500 {
            format!("{}...", &response_text[..500])
        } else {
            response_text.clone()
        };
        info!("API response preview: {}", preview);

        // Parse as raw JSON Value to see the actual structure
        match serde_json::from_str::<Value>(&response_text) {
            Ok(json_value) => {
                info!("Successfully parsed API response as JSON");
                Ok(json_value)
            }
            Err(e) => {
                error!("Failed to parse API response as JSON: {}", e);
                error!("Response body: {}", response_text);
                Err(anyhow::anyhow!("Failed to parse API response as JSON: {}", e))
            }
        }
    }

    /// Call POST /tokens/multi endpoint with batch of token addresses
    pub async fn post_tokens_multi(&self, request_body: &Value) -> Result<Value> {
        let url = format!("{}/tokens/multi", self.base_url);

        info!("Calling POST /tokens/multi with {} tokens",
              request_body.get("tokens")
                  .and_then(|t| t.as_array())
                  .map(|a| a.len())
                  .unwrap_or(0));

        let response = self
            .client
            .post(&url)
            .header("x-api-key", &self.api_key)
            .header("Content-Type", "application/json")
            .json(request_body)
            .send()
            .await
            .context("Failed to send request to Solana Tracker /tokens/multi")?;

        let status = response.status();
        info!("POST /tokens/multi response status: {}", status);

        if !status.is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());

            error!("POST /tokens/multi failed with status {}: {}", status, error_text);

            return Err(anyhow::anyhow!(
                "POST /tokens/multi failed with status {}: {}",
                status,
                error_text
            ));
        }

        let response_text = response
            .text()
            .await
            .context("Failed to read response body")?;

        // Parse the response
        match serde_json::from_str::<Value>(&response_text) {
            Ok(json_response) => {
                info!("Successfully parsed POST /tokens/multi response");
                Ok(json_response)
            }
            Err(e) => {
                error!("Failed to parse POST /tokens/multi response: {}", e);
                error!("Response body: {}", response_text);
                Err(anyhow::anyhow!("Failed to parse response: {}", e))
            }
        }
    }

    pub async fn health_check(&self) -> Result<bool> {
        let url = format!("{}/credits", self.base_url);
        
        info!("Performing health check: {}", url);

        let response = self
            .client
            .get(&url)
            .header("x-api-key", &self.api_key)
            .timeout(Duration::from_secs(10))
            .send()
            .await
            .context("Failed to perform health check")?;

        let is_healthy = response.status().is_success();
        
        if is_healthy {
            info!("Health check passed");
        } else {
            warn!("Health check failed with status: {}", response.status());
        }

        Ok(is_healthy)
    }
}
