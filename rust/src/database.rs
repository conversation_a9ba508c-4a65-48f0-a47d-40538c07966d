use anyhow::{Context, Result};
use serde_json::Value;
use sqlx::{PgPool, Row};
use tracing::{info, warn, error};

#[derive(Clone)]
pub struct Database {
    pool: PgPool,
}

impl Database {
    pub async fn new(database_url: &str) -> Result<Self> {
        info!("Connecting to PostgreSQL database...");
        
        let pool = PgPool::connect(database_url)
            .await
            .context("Failed to connect to PostgreSQL database")?;

        info!("Successfully connected to PostgreSQL database");

        let db = Self { pool };
        
        // Run migrations
        db.run_migrations().await?;
        
        Ok(db)
    }

    async fn run_migrations(&self) -> Result<()> {
        info!("Running database migrations...");

        // Create tokens table if it doesn't exist
        sqlx::query(
            r#"
            CREATE TABLE IF NOT EXISTS tokens (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                token_address VARCHAR(44) NOT NULL UNIQUE,
                creator_address VARCHAR(44), -- Made optional to handle tokens without creator
                raw_data JSONB NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
            "#
        )
        .execute(&self.pool)
        .await
        .context("Failed to create tokens table")?;



        // Create indexes separately
        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tokens_token_address ON tokens(token_address)")
            .execute(&self.pool)
            .await
            .context("Failed to create token_address index")?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tokens_creator_address ON tokens(creator_address)")
            .execute(&self.pool)
            .await
            .context("Failed to create creator_address index")?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tokens_created_at ON tokens(created_at)")
            .execute(&self.pool)
            .await
            .context("Failed to create created_at index")?;

        sqlx::query("CREATE INDEX IF NOT EXISTS idx_tokens_raw_data_gin ON tokens USING GIN(raw_data)")
            .execute(&self.pool)
            .await
            .context("Failed to create JSONB GIN index")?;



        info!("Database migrations completed successfully");
        Ok(())
    }

    pub async fn save_tokens(&self, tokens_data: &[Value]) -> Result<usize> {
        info!("Saving {} tokens to database", tokens_data.len());

        // Start a transaction to ensure atomicity
        let mut tx = self.pool.begin().await
            .context("Failed to start database transaction")?;

        let mut saved_count = 0;
        let mut updated_count = 0;

        for token_data in tokens_data {
            // Extract token address and creator from the JSON
            let token_address = match token_data
                .get("token")
                .and_then(|t| t.get("mint"))
                .and_then(|m| m.as_str()) {
                Some(addr) => addr,
                None => {
                    error!("Token missing mint address, skipping: {:?}",
                           token_data.get("token").and_then(|t| t.get("name")));
                    continue;
                }
            };

            let creator_address = token_data
                .get("token")
                .and_then(|t| t.get("creation"))
                .and_then(|c| c.get("creator"))
                .and_then(|cr| cr.as_str());

            // Log if creator is missing but continue processing
            if creator_address.is_none() {
                warn!("Token {} has no creator address (creation field is null), saving with NULL creator", token_address);
            }

            // Use UPSERT to handle duplicates
            let result = sqlx::query(
                r#"
                INSERT INTO tokens (token_address, creator_address, raw_data, updated_at)
                VALUES ($1, $2, $3, NOW())
                ON CONFLICT (token_address) 
                DO UPDATE SET 
                    creator_address = EXCLUDED.creator_address,
                    raw_data = EXCLUDED.raw_data,
                    updated_at = NOW()
                RETURNING (xmax = 0) AS inserted
                "#
            )
            .bind(token_address)
            .bind(creator_address)
            .bind(token_data)
            .fetch_one(&mut *tx)
            .await;

            match result {
                Ok(row) => {
                    let inserted: bool = row.get("inserted");
                    if inserted {
                        saved_count += 1;
                    } else {
                        updated_count += 1;
                    }
                }
                Err(e) => {
                    error!("Failed to save token {}: {}", token_address, e);
                    // Continue with other tokens instead of failing completely
                }
            }
        }

        // Commit the transaction
        tx.commit().await
            .context("Failed to commit database transaction")?;

        info!(
            "Database operation completed: {} new tokens saved, {} tokens updated",
            saved_count, updated_count
        );

        Ok(saved_count + updated_count)
    }

    pub async fn get_token_count(&self) -> Result<i64> {
        let row = sqlx::query("SELECT COUNT(*) as count FROM tokens")
            .fetch_one(&self.pool)
            .await
            .context("Failed to get token count")?;

        Ok(row.get("count"))
    }



    pub async fn health_check(&self) -> Result<bool> {
        match sqlx::query("SELECT 1").fetch_one(&self.pool).await {
            Ok(_) => Ok(true),
            Err(e) => {
                error!("Database health check failed: {}", e);
                Ok(false)
            }
        }
    }

    pub async fn get_unique_creators(&self) -> Result<Vec<String>> {
        let rows = sqlx::query("SELECT DISTINCT creator_address FROM tokens WHERE creator_address IS NOT NULL ORDER BY creator_address")
            .fetch_all(&self.pool)
            .await
            .context("Failed to fetch unique creators")?;

        let creators: Vec<String> = rows
            .into_iter()
            .map(|row| row.get::<String, _>("creator_address"))
            .collect();

        info!("Found {} unique creators in database (excluding tokens with NULL creator)", creators.len());
        Ok(creators)
    }



    pub fn get_pool(&self) -> &PgPool {
        &self.pool
    }

    /// Get all tokens that don't have trades data yet
    pub async fn get_tokens_without_trades(&self) -> Result<Vec<String>> {
        let query = "
            SELECT t.token_address
            FROM tokens t
            LEFT JOIN token_trades tt ON t.token_address = tt.token_address
            WHERE tt.token_address IS NULL
            ORDER BY t.created_at DESC
        ";

        let rows = sqlx::query(query)
            .fetch_all(&self.pool)
            .await
            .context("Failed to fetch tokens without trades")?;

        let token_addresses: Vec<String> = rows
            .iter()
            .map(|row| row.get::<String, _>("token_address"))
            .collect();

        Ok(token_addresses)
    }
}
