use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio_postgres::{Client as PgClient, Error as PgError};
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct TradeData {
    pub tx: String,
    pub amount: f64,
    #[serde(rename = "priceUsd")]
    pub price_usd: f64,
    pub volume: f64,
    #[serde(rename = "volumeSol")]
    pub volume_sol: Option<f64>,
    #[serde(rename = "type")]
    pub trade_type: String,
    pub wallet: String,
    pub time: i64,
    pub program: String,
    pub pools: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TradesResponse {
    pub trades: Vec<TradeData>,
    #[serde(rename = "nextCursor")]
    pub next_cursor: Option<String>,
    #[serde(rename = "hasNextPage")]
    pub has_next_page: bool,
    #[serde(rename = "sortDirection")]
    pub sort_direction: String,
}

pub struct SolanaTrackerClient {
    client: Client,
    api_key: String,
    base_url: String,
}

impl SolanaTrackerClient {
    pub fn new(api_key: String) -> Self {
        Self {
            client: Client::new(),
            api_key,
            base_url: "https://data.solanatracker.io".to_string(),
        }
    }

    /// Fetch ALL trades for a specific token from creation (always ASC order)
    /// This ensures we have complete historical data for ML analysis
    pub async fn get_token_trades(
        &self,
        token_address: &str,
        limit: Option<u32>,
    ) -> Result<TradesResponse, Box<dyn std::error::Error>> {
        let mut url = format!("{}/trades/{}", self.base_url, token_address);

        let mut params = Vec::new();
        // Always use ascending order to get trades from token creation
        params.push("sortDirection=asc".to_string());

        // Use high limit to get as many trades as possible
        let limit = limit.unwrap_or(1000); // Default to 1000 for comprehensive data
        params.push(format!("limit={}", limit));
        
        if !params.is_empty() {
            url.push('?');
            url.push_str(&params.join("&"));
        }

        let response = self
            .client
            .get(&url)
            .header("x-api-key", &self.api_key)
            .header("Content-Type", "application/json")
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(format!("API request failed with status: {}", response.status()).into());
        }

        let trades_response: TradesResponse = response.json().await?;
        Ok(trades_response)
    }

    /// Fetch ALL trades for a token with pagination support
    /// This ensures we get complete historical data even for very active tokens
    pub async fn get_all_token_trades(
        &self,
        token_address: &str,
    ) -> Result<Vec<TradeData>, Box<dyn std::error::Error>> {
        let mut all_trades = Vec::new();
        let mut current_limit = 1000;
        let mut page = 1;

        loop {
            println!("📄 Fetching page {} for token {} (limit: {})", page, token_address, current_limit);

            let trades_response = self.get_token_trades(token_address, Some(current_limit)).await?;

            let trades_count = trades_response.trades.len();
            println!("📊 Received {} trades on page {}", trades_count, page);

            // Add trades to our collection
            all_trades.extend(trades_response.trades);

            // Check if we need to fetch more pages
            if !trades_response.has_next_page || trades_count == 0 {
                println!("✅ Completed fetching all trades. Total: {} trades", all_trades.len());
                break;
            }

            page += 1;

            // Add small delay to respect rate limits
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        }

        Ok(all_trades)
    }

    /// Store trades data in the database
    pub async fn store_trades_in_db(
        &self,
        db_client: &PgClient,
        token_address: &str,
        trades: &[TradeData],
    ) -> Result<i32, PgError> {
        let mut inserted_count = 0;

        for trade in trades {
            // Convert timestamp from milliseconds to timestamp
            let trade_time = chrono::NaiveDateTime::from_timestamp_opt(trade.time / 1000, 0)
                .unwrap_or_default();
            let trade_time = chrono::DateTime::<chrono::Utc>::from_utc(trade_time, chrono::Utc);

            // Determine trade size category
            let trade_size_category = match trade.volume {
                v if v < 10.0 => "micro",
                v if v < 100.0 => "small", 
                v if v < 1000.0 => "medium",
                v if v < 10000.0 => "large",
                _ => "whale",
            };

            // Convert pools to JSON
            let pools_json = serde_json::to_value(&trade.pools)?;

            // Convert entire trade to JSON for raw_data
            let raw_data = serde_json::to_value(trade)?;

            let query = r#"
                INSERT INTO token_trades (
                    token_address, tx_hash, trade_time, trade_type, wallet_address,
                    token_amount, price_usd, volume_usd, volume_sol,
                    program, pools, trade_size_category, raw_data
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                ON CONFLICT (tx_hash) DO UPDATE SET
                    trade_time = EXCLUDED.trade_time,
                    trade_type = EXCLUDED.trade_type,
                    wallet_address = EXCLUDED.wallet_address,
                    token_amount = EXCLUDED.token_amount,
                    price_usd = EXCLUDED.price_usd,
                    volume_usd = EXCLUDED.volume_usd,
                    volume_sol = EXCLUDED.volume_sol,
                    program = EXCLUDED.program,
                    pools = EXCLUDED.pools,
                    trade_size_category = EXCLUDED.trade_size_category,
                    raw_data = EXCLUDED.raw_data
            "#;

            match db_client
                .execute(
                    query,
                    &[
                        &token_address,
                        &trade.tx,
                        &trade_time,
                        &trade.trade_type,
                        &trade.wallet,
                        &(trade.amount as f64),
                        &(trade.price_usd as f64),
                        &(trade.volume as f64),
                        &trade.volume_sol.map(|v| v as f64),
                        &trade.program,
                        &pools_json,
                        &trade_size_category,
                        &raw_data,
                    ],
                )
                .await
            {
                Ok(_) => inserted_count += 1,
                Err(e) => {
                    eprintln!("Failed to insert trade {}: {}", trade.tx, e);
                }
            }
        }

        Ok(inserted_count)
    }

    /// Fetch and store ALL historical trades for a token (from creation)
    /// Uses pagination to ensure complete data collection
    pub async fn fetch_and_store_token_trades(
        &self,
        db_client: &PgClient,
        token_address: &str,
        _limit: Option<u32>, // Ignored - we always fetch all trades
    ) -> Result<(usize, i32), Box<dyn std::error::Error>> {
        println!("🔍 Fetching ALL historical trades for token: {} (from creation)", token_address);

        // Get ALL trades using pagination
        let all_trades = self.get_all_token_trades(token_address).await?;

        let trades_fetched = all_trades.len();

        if trades_fetched == 0 {
            println!("📭 No trades found for token: {}", token_address);
            return Ok((0, 0));
        }

        println!("💾 Storing {} trades in database...", trades_fetched);

        let trades_inserted = self
            .store_trades_in_db(db_client, token_address, &all_trades)
            .await?;

        println!(
            "✅ Successfully stored {}/{} trades for token: {}",
            trades_inserted, trades_fetched, token_address
        );

        // Log some statistics about the trades
        if trades_fetched > 0 {
            let first_trade_time = all_trades.first().unwrap().time;
            let last_trade_time = all_trades.last().unwrap().time;
            let duration_hours = (last_trade_time - first_trade_time) as f64 / (1000.0 * 3600.0);

            println!(
                "📈 Trade timeline: {:.2} hours of activity ({} to {})",
                duration_hours,
                chrono::DateTime::from_timestamp(first_trade_time / 1000, 0)
                    .unwrap_or_default()
                    .format("%Y-%m-%d %H:%M:%S"),
                chrono::DateTime::from_timestamp(last_trade_time / 1000, 0)
                    .unwrap_or_default()
                    .format("%Y-%m-%d %H:%M:%S")
            );
        }

        Ok((trades_fetched, trades_inserted))
    }

    /// Update migration features after storing trades
    pub async fn update_migration_features(
        &self,
        db_client: &PgClient,
        token_address: &str,
    ) -> Result<(), PgError> {
        let query = r#"
            SELECT extract_migration_features($1, NULL, '{}'::JSONB, NOW(), NOW())
        "#;

        db_client.execute(query, &[&token_address]).await?;
        println!("Updated migration features for token: {}", token_address);
        
        Ok(())
    }

    /// Get migration analysis for a token
    pub async fn get_migration_analysis(
        &self,
        db_client: &PgClient,
        token_address: &str,
    ) -> Result<Option<MigrationAnalysis>, PgError> {
        let query = r#"
            SELECT migration_probability, key_indicators, risk_factors, recommendation
            FROM analyze_trading_patterns($1)
        "#;

        let rows = db_client.query(query, &[&token_address]).await?;
        
        if let Some(row) = rows.first() {
            let analysis = MigrationAnalysis {
                migration_probability: row.get::<_, f64>(0),
                key_indicators: row.get::<_, Value>(1),
                risk_factors: row.get::<_, Value>(2),
                recommendation: row.get::<_, String>(3),
            };
            Ok(Some(analysis))
        } else {
            Ok(None)
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MigrationAnalysis {
    pub migration_probability: f64,
    pub key_indicators: Value,
    pub risk_factors: Value,
    pub recommendation: String,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_solanatracker_client_creation() {
        let client = SolanaTrackerClient::new("test-api-key".to_string());
        assert_eq!(client.api_key, "test-api-key");
        assert_eq!(client.base_url, "https://data.solanatracker.io");
    }
}
