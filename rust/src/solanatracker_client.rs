use anyhow::{Context, Result};
use reqwest::Client;
use serde_json::Value;
use std::time::Duration;
use tracing::{info, warn, error};

#[derive(Clone)]
pub struct SolanaTrackerClient {
    client: Client,
    api_key: String,
    base_url: String,
}

impl SolanaTrackerClient {
    pub fn new(api_key: String) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            api_key,
            base_url: "https://data.solanatracker.io".to_string(),
        }
    }

    /// Fetch trades for a specific token with retry logic
    /// For pump.fun tokens, poolAddress = tokenAddress
    pub async fn fetch_token_trades(&self, token_address: &str, limit: Option<u32>) -> Result<Vec<Value>> {
        let limit = limit.unwrap_or(1000);
        // Use correct endpoint: /trades/{tokenAddress}/{poolAddress}
        // For pump.fun tokens, poolAddress = tokenAddress
        let url = format!("{}/trades/{}/{}", self.base_url, token_address, token_address);

        info!("Fetching trades for pump.fun token: {} (limit: {})", token_address, limit);
        
        // First attempt
        match self.fetch_trades_request(&url, limit).await {
            Ok(trades) => {
                info!("Successfully fetched {} trades for token {}", trades.len(), token_address);
                return Ok(trades);
            }
            Err(e) => {
                // Check if it's a 404 error (no trades available)
                if e.to_string().contains("404") {
                    info!("No trades available for token {} (404 - normal for tokens without trades)", token_address);
                    return Ok(Vec::new());
                }

                warn!("First attempt failed for token {}: {}", token_address, e);

                // Wait before retry (only for non-404 errors)
                tokio::time::sleep(Duration::from_millis(1000)).await;

                // Retry attempt
                match self.fetch_trades_request(&url, limit).await {
                    Ok(trades) => {
                        info!("Retry successful: fetched {} trades for token {}", trades.len(), token_address);
                        return Ok(trades);
                    }
                    Err(retry_error) => {
                        // Check if retry also got 404
                        if retry_error.to_string().contains("404") {
                            info!("No trades available for token {} after retry (404 - normal)", token_address);
                            return Ok(Vec::new());
                        }

                        error!("Retry failed for token {}: {}", token_address, retry_error);
                        return Err(retry_error.context(format!("Failed to fetch trades for token {} after retry", token_address)));
                    }
                }
            }
        }
    }

    /// Internal method to make the actual API request
    async fn fetch_trades_request(&self, url: &str, limit: u32) -> Result<Vec<Value>> {
        let response = self
            .client
            .get(url)
            .header("X-API-KEY", &self.api_key)
            .query(&[
                ("limit", limit.to_string()),
                ("sortDirection", "asc".to_string()),
            ])
            .send()
            .await
            .context("Failed to send request to SolanaTracker API")?;

        // Handle 404 as "no trades available" (not an error)
        if response.status() == 404 {
            info!("No trades found for token (404 - token may not have trades yet)");
            return Ok(Vec::new());
        }

        if !response.status().is_success() {
            return Err(anyhow::anyhow!(
                "API request failed with status: {}",
                response.status()
            ));
        }

        let trades_data: Value = response
            .json()
            .await
            .context("Failed to parse trades response as JSON")?;

        // Extract trades array from response
        let trades = if let Some(trades_array) = trades_data.as_array() {
            trades_array.clone()
        } else if let Some(obj) = trades_data.as_object() {
            if let Some(trades_array) = obj.get("trades").and_then(|t| t.as_array()) {
                trades_array.clone()
            } else {
                Vec::new()
            }
        } else {
            Vec::new()
        };

        Ok(trades)
    }

    /// Save trades to database
    pub async fn save_trades_to_db(
        &self,
        database: &crate::database::Database,
        token_address: &str,
        trades: &[Value],
    ) -> Result<usize> {
        if trades.is_empty() {
            info!("No trades to save for token {}", token_address);
            return Ok(0);
        }

        let mut saved_count = 0;
        let pool = database.get_pool();

        for (index, trade) in trades.iter().enumerate() {
            // Try to get signature, if not available create unique hash
            let tx_hash = match trade.get("signature").and_then(|s| s.as_str()) {
                Some(sig) if !sig.is_empty() => sig.to_string(),
                _ => {
                    // Create unique hash from trade data
                    let unique_data = format!("{}_{}_{}_{}",
                        token_address,
                        trade.get("timestamp").and_then(|t| t.as_i64()).unwrap_or(0),
                        trade.get("trader").and_then(|t| t.as_str()).unwrap_or(""),
                        index
                    );
                    format!("generated_{}", unique_data.chars().take(64).collect::<String>())
                }
            };

            let trade_type = trade.get("type")
                .and_then(|t| t.as_str())
                .unwrap_or("unknown");

            let token_amount = trade.get("amount")
                .and_then(|a| a.as_f64())
                .unwrap_or(0.0);

            let price_usd = trade.get("priceUSD")
                .and_then(|p| p.as_f64())
                .unwrap_or(0.0);

            let wallet_address = trade.get("trader")
                .and_then(|t| t.as_str())
                .unwrap_or("");

            let trade_time = trade.get("timestamp")
                .and_then(|t| t.as_i64())
                .map(|ts| chrono::DateTime::from_timestamp(ts, 0))
                .flatten()
                .unwrap_or_else(|| chrono::Utc::now());

            // Calculate volume_usd (token_amount * price_usd)
            let volume_usd = token_amount * price_usd;

            let query = "
                INSERT INTO token_trades (
                    token_address, tx_hash, trade_type, token_amount, price_usd,
                    wallet_address, trade_time, volume_usd, raw_data
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                ON CONFLICT (tx_hash) DO NOTHING
            ";

            match sqlx::query(query)
                .bind(token_address)
                .bind(&tx_hash)
                .bind(trade_type)
                .bind(token_amount)
                .bind(price_usd)
                .bind(wallet_address)
                .bind(trade_time)
                .bind(volume_usd)
                .bind(trade)
                .execute(pool)
                .await
            {
                Ok(result) => {
                    if result.rows_affected() > 0 {
                        saved_count += 1;
                    }
                }
                Err(e) => {
                    error!("Failed to save trade {} for token {}: {}", tx_hash, token_address, e);
                    return Err(e.into());
                }
            }
        }

        info!("Saved {} trades for token {}", saved_count, token_address);
        Ok(saved_count)
    }
}
