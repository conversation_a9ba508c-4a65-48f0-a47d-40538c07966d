use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio_postgres::{Client as PgClient, Error as PgError};
use serde_json::Value;

#[derive(Debug, Serialize, Deserialize)]
pub struct TradeData {
    pub tx: String,
    pub amount: f64,
    #[serde(rename = "priceUsd")]
    pub price_usd: f64,
    pub volume: f64,
    #[serde(rename = "volumeSol")]
    pub volume_sol: Option<f64>,
    #[serde(rename = "type")]
    pub trade_type: String,
    pub wallet: String,
    pub time: i64,
    pub program: String,
    pub pools: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TradesResponse {
    pub trades: Vec<TradeData>,
    #[serde(rename = "nextCursor")]
    pub next_cursor: Option<String>,
    #[serde(rename = "hasNextPage")]
    pub has_next_page: bool,
    #[serde(rename = "sortDirection")]
    pub sort_direction: String,
}

pub struct SolanaTrackerClient {
    client: Client,
    api_key: String,
    base_url: String,
}

impl SolanaTrackerClient {
    pub fn new(api_key: String) -> Self {
        Self {
            client: Client::new(),
            api_key,
            base_url: "https://data.solanatracker.io".to_string(),
        }
    }

    /// Fetch trades for a specific token
    pub async fn get_token_trades(
        &self,
        token_address: &str,
        limit: Option<u32>,
        sort_direction: Option<&str>,
    ) -> Result<TradesResponse, Box<dyn std::error::Error>> {
        let mut url = format!("{}/trades/{}", self.base_url, token_address);
        
        let mut params = Vec::new();
        if let Some(limit) = limit {
            params.push(format!("limit={}", limit));
        }
        if let Some(sort_dir) = sort_direction {
            params.push(format!("sortDirection={}", sort_dir));
        }
        
        if !params.is_empty() {
            url.push('?');
            url.push_str(&params.join("&"));
        }

        let response = self
            .client
            .get(&url)
            .header("x-api-key", &self.api_key)
            .header("Content-Type", "application/json")
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(format!("API request failed with status: {}", response.status()).into());
        }

        let trades_response: TradesResponse = response.json().await?;
        Ok(trades_response)
    }

    /// Store trades data in the database
    pub async fn store_trades_in_db(
        &self,
        db_client: &PgClient,
        token_address: &str,
        trades: &[TradeData],
    ) -> Result<i32, PgError> {
        let mut inserted_count = 0;

        for trade in trades {
            // Convert timestamp from milliseconds to timestamp
            let trade_time = chrono::NaiveDateTime::from_timestamp_opt(trade.time / 1000, 0)
                .unwrap_or_default();
            let trade_time = chrono::DateTime::<chrono::Utc>::from_utc(trade_time, chrono::Utc);

            // Determine trade size category
            let trade_size_category = match trade.volume {
                v if v < 10.0 => "micro",
                v if v < 100.0 => "small", 
                v if v < 1000.0 => "medium",
                v if v < 10000.0 => "large",
                _ => "whale",
            };

            // Convert pools to JSON
            let pools_json = serde_json::to_value(&trade.pools)?;

            // Convert entire trade to JSON for raw_data
            let raw_data = serde_json::to_value(trade)?;

            let query = r#"
                INSERT INTO token_trades (
                    token_address, tx_hash, trade_time, trade_type, wallet_address,
                    token_amount, price_usd, volume_usd, volume_sol,
                    program, pools, trade_size_category, raw_data
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                ON CONFLICT (tx_hash) DO UPDATE SET
                    trade_time = EXCLUDED.trade_time,
                    trade_type = EXCLUDED.trade_type,
                    wallet_address = EXCLUDED.wallet_address,
                    token_amount = EXCLUDED.token_amount,
                    price_usd = EXCLUDED.price_usd,
                    volume_usd = EXCLUDED.volume_usd,
                    volume_sol = EXCLUDED.volume_sol,
                    program = EXCLUDED.program,
                    pools = EXCLUDED.pools,
                    trade_size_category = EXCLUDED.trade_size_category,
                    raw_data = EXCLUDED.raw_data
            "#;

            match db_client
                .execute(
                    query,
                    &[
                        &token_address,
                        &trade.tx,
                        &trade_time,
                        &trade.trade_type,
                        &trade.wallet,
                        &(trade.amount as f64),
                        &(trade.price_usd as f64),
                        &(trade.volume as f64),
                        &trade.volume_sol.map(|v| v as f64),
                        &trade.program,
                        &pools_json,
                        &trade_size_category,
                        &raw_data,
                    ],
                )
                .await
            {
                Ok(_) => inserted_count += 1,
                Err(e) => {
                    eprintln!("Failed to insert trade {}: {}", trade.tx, e);
                }
            }
        }

        Ok(inserted_count)
    }

    /// Fetch and store trades for a token in one operation
    pub async fn fetch_and_store_token_trades(
        &self,
        db_client: &PgClient,
        token_address: &str,
        limit: Option<u32>,
    ) -> Result<(usize, i32), Box<dyn std::error::Error>> {
        println!("Fetching trades for token: {}", token_address);
        
        let trades_response = self
            .get_token_trades(token_address, limit, Some("asc"))
            .await?;

        let trades_fetched = trades_response.trades.len();
        
        if trades_fetched == 0 {
            println!("No trades found for token: {}", token_address);
            return Ok((0, 0));
        }

        println!("Fetched {} trades, storing in database...", trades_fetched);
        
        let trades_inserted = self
            .store_trades_in_db(db_client, token_address, &trades_response.trades)
            .await?;

        println!(
            "Successfully stored {}/{} trades for token: {}",
            trades_inserted, trades_fetched, token_address
        );

        Ok((trades_fetched, trades_inserted))
    }

    /// Update migration features after storing trades
    pub async fn update_migration_features(
        &self,
        db_client: &PgClient,
        token_address: &str,
    ) -> Result<(), PgError> {
        let query = r#"
            SELECT extract_migration_features($1, NULL, '{}'::JSONB, NOW(), NOW())
        "#;

        db_client.execute(query, &[&token_address]).await?;
        println!("Updated migration features for token: {}", token_address);
        
        Ok(())
    }

    /// Get migration analysis for a token
    pub async fn get_migration_analysis(
        &self,
        db_client: &PgClient,
        token_address: &str,
    ) -> Result<Option<MigrationAnalysis>, PgError> {
        let query = r#"
            SELECT migration_probability, key_indicators, risk_factors, recommendation
            FROM analyze_trading_patterns($1)
        "#;

        let rows = db_client.query(query, &[&token_address]).await?;
        
        if let Some(row) = rows.first() {
            let analysis = MigrationAnalysis {
                migration_probability: row.get::<_, f64>(0),
                key_indicators: row.get::<_, Value>(1),
                risk_factors: row.get::<_, Value>(2),
                recommendation: row.get::<_, String>(3),
            };
            Ok(Some(analysis))
        } else {
            Ok(None)
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MigrationAnalysis {
    pub migration_probability: f64,
    pub key_indicators: Value,
    pub risk_factors: Value,
    pub recommendation: String,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_solanatracker_client_creation() {
        let client = SolanaTrackerClient::new("test-api-key".to_string());
        assert_eq!(client.api_key, "test-api-key");
        assert_eq!(client.base_url, "https://data.solanatracker.io");
    }
}
