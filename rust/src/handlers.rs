use axum::{
    extract::{State, Query},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use tracing::{info, error, warn, debug};

use crate::AppState;
use crate::token_processor::TokenProcessor;

#[derive(Debug, Deserialize)]
pub struct CollectParams {
    pub tokens_per_creator: Option<usize>,
    pub limit: Option<usize>, // Limit number of creators to process (for testing)
}



#[derive(Debug, Serialize, Deserialize)]
pub struct CollectResponse {
    pub success: bool,
    pub message: String,
    pub tokens_count: usize,
    pub db_saved_count: usize,
    pub timestamp: DateTime<Utc>,
}





#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
    pub timestamp: DateTime<Utc>,
}

pub async fn collect_graduated_tokens(
    State(state): State<AppState>,
    Query(params): Query<CollectParams>,
) -> Result<Json<CollectResponse>, (StatusCode, Json<ErrorResponse>)> {
    info!("Starting collection of graduated tokens");

    // Fetch data from Solana Tracker API
    let api_data = match state.client.get_graduated_tokens().await {
        Ok(data) => {
            info!("Successfully fetched raw data from API");
            data
        }
        Err(e) => {
            error!("Failed to fetch graduated tokens: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "API_FETCH_ERROR".to_string(),
                    message: format!("Failed to fetch data from Solana Tracker API: {}", e),
                    timestamp: Utc::now(),
                }),
            ));
        }
    };

    // Filter and save data to PostgreSQL database
    let db_saved_count = if let Some(tokens_array) = api_data.as_array() {
        // Filter tokens to only include those created on pump.fun
        let pump_fun_tokens: Vec<serde_json::Value> = tokens_array
            .iter()
            .filter(|token| {
                token
                    .get("token")
                    .and_then(|t| t.get("createdOn"))
                    .and_then(|co| co.as_str())
                    .map(|s| s == "https://pump.fun")
                    .unwrap_or(false)
            })
            .cloned()
            .collect();

        info!(
            "Filtered {} pump.fun tokens from {} total tokens",
            pump_fun_tokens.len(),
            tokens_array.len()
        );

        if pump_fun_tokens.is_empty() {
            info!("No pump.fun tokens found to save");
            0
        } else {
            match state.database.save_tokens(&pump_fun_tokens).await {
                Ok(count) => {
                    info!("Successfully saved {} pump.fun tokens to database", count);
                    count
                }
                Err(e) => {
                    error!("Failed to save tokens to database: {}", e);
                    return Err((
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse {
                            error: "DATABASE_ERROR".to_string(),
                            message: format!("Failed to save data to database: {}", e),
                            timestamp: Utc::now(),
                        }),
                    ));
                }
            }
        }
    } else {
        error!("API data is not an array");
        return Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "DATA_FORMAT_ERROR".to_string(),
                message: "API data is not in expected array format".to_string(),
                timestamp: Utc::now(),
            }),
        ));
    };

    // Try to count tokens if it's an array
    let tokens_count = if let Some(array) = api_data.as_array() {
        array.len()
    } else if let Some(obj) = api_data.as_object() {
        if let Some(tokens) = obj.get("tokens").and_then(|t| t.as_array()) {
            tokens.len()
        } else {
            1 // Single object
        }
    } else {
        1 // Unknown structure
    };

    // Get unique creators and fetch their assets from Helius
    let all_creators = match state.database.get_unique_creators().await {
        Ok(creators) => creators,
        Err(e) => {
            error!("Failed to get unique creators: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "DATABASE_ERROR".to_string(),
                    message: format!("Failed to get unique creators: {}", e),
                    timestamp: Utc::now(),
                }),
            ));
        }
    };

    // Apply limit to prevent system overload during testing
    let total_creators = all_creators.len();
    let creators: Vec<String> = if let Some(limit) = params.limit {
        all_creators.into_iter().take(limit).collect()
    } else {
        all_creators
    };

    info!("Starting Helius asset collection for {} unique creators (limited from {} total available)", creators.len(), total_creators);

    let mut all_token_addresses = Vec::new();
    let mut helius_assets_collected = 0;
    let mut helius_errors = 0;

    // Create token processor for later use
    let token_processor = TokenProcessor::new(
        state.client.clone(),
        state.database.clone(),
    );

    // Get tokens per creator limit (default to 10 for efficiency)
    let tokens_per_creator = params.tokens_per_creator.unwrap_or(10);

    for (index, creator) in creators.iter().enumerate() {
        info!("Processing creator {}/{}: {} (getting {} most recent tokens)",
              index + 1, creators.len(), creator, tokens_per_creator);

        // Add timeout to prevent hanging
        let timeout_duration = tokio::time::Duration::from_secs(30);
        match tokio::time::timeout(timeout_duration, state.helius_client.get_recent_assets_by_creator(creator, tokens_per_creator)).await {
            Ok(Ok(assets)) => {
                if !assets.is_empty() {
                    // Extract token addresses directly from assets (without saving assets)
                    let token_addresses = token_processor.extract_token_addresses_from_assets(&assets);
                    all_token_addresses.extend(token_addresses);
                    helius_assets_collected += assets.len();

                    info!("Collected {} recent assets for creator {}", assets.len(), creator);
                } else {
                    info!("No assets found for creator {}", creator);
                }
            }
            Ok(Err(e)) => {
                error!("Failed to fetch assets for creator {}: {}", creator, e);
                helius_errors += 1;
            }
            Err(_timeout) => {
                error!("Timeout while fetching assets for creator {}", creator);
                helius_errors += 1;
            }
        }

        // Add delay between requests to respect rate limits (optimized to 101ms)
        if index < creators.len() - 1 {
            tokio::time::sleep(tokio::time::Duration::from_millis(101)).await;
        }
    }

    // Check for duplicates before removing them
    let total_addresses_before = all_token_addresses.len();
    all_token_addresses.sort();
    all_token_addresses.dedup();
    let total_addresses_after = all_token_addresses.len();
    let duplicates_removed = total_addresses_before - total_addresses_after;

    if duplicates_removed > 0 {
        warn!("Found and removed {} duplicate token addresses! This should not happen if each token has only one creator.", duplicates_removed);
    }

    info!(
        "Collected {} unique token addresses from {} assets across {} creators (removed {} duplicates)",
        all_token_addresses.len(),
        helius_assets_collected,
        creators.len(),
        duplicates_removed
    );

    // Process token addresses directly
    let mut tokens_processed = 0;
    let mut tokens_saved_to_db = 0;
    let mut processing_errors = 0;

    if !all_token_addresses.is_empty() {
        info!("Processing {} unique token addresses", all_token_addresses.len());
        debug!("🔍 DEBUG: Starting token processing with {} addresses", all_token_addresses.len());
        debug!("🔍 DEBUG: First 5 token addresses: {:?}", &all_token_addresses[..std::cmp::min(5, all_token_addresses.len())]);

        // Process all collected token addresses
        info!("Processing {} unique token addresses", all_token_addresses.len());

        // Process all tokens
        debug!("🔍 DEBUG: Calling token_processor.process_token_addresses");
        match token_processor.process_token_addresses(all_token_addresses).await {
            Ok(processing_stats) => {
                tokens_processed = processing_stats.tokens_processed;
                tokens_saved_to_db = processing_stats.tokens_saved_to_db;
                processing_errors = processing_stats.batches_failed;

                info!(
                    "Token processing completed: {}/{} tokens processed, {} saved to database",
                    processing_stats.tokens_processed,
                    processing_stats.total_tokens_extracted,
                    processing_stats.tokens_saved_to_db
                );
            }
            Err(e) => {
                error!("Failed to process token addresses: {}", e);
                processing_errors = 1;
            }
        }
    } else {
        info!("No token addresses to process");
    }

    // 🆕 COLLECT TRADES - Always at the end of workflow
    let mut trades_collected = 0;
    let mut trades_tokens_processed = 0;

    info!("🔄 Starting trades collection for all tokens without trades data");

    // Get all tokens that don't have trades yet
    let tokens_without_trades = match state.database.get_tokens_without_trades().await {
        Ok(tokens) => tokens,
        Err(e) => {
            error!("Failed to get tokens without trades: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "DATABASE_ERROR".to_string(),
                    message: format!("Failed to get tokens without trades: {}", e),
                    timestamp: Utc::now(),
                }),
            ));
        }
    };

    info!("📊 Found {} tokens without trades data", tokens_without_trades.len());

    // Process each token individually with 1 req/sec rate limit
    for (index, token_address) in tokens_without_trades.iter().enumerate() {
        info!("🔍 Processing trades for token {}/{}: {}",
              index + 1, tokens_without_trades.len(), token_address);

        // Fetch trades for this token (with retry logic built-in)
        match state.st_client.fetch_token_trades(token_address, Some(1000)).await {
            Ok(trades) => {
                // Save trades to database
                match state.st_client.save_trades_to_db(&state.database, token_address, &trades).await {
                    Ok(saved_count) => {
                        trades_collected += saved_count;
                        trades_tokens_processed += 1;
                        info!("✅ Saved {} trades for token {}", saved_count, token_address);
                    }
                    Err(e) => {
                        error!("❌ Failed to save trades for token {}: {}", token_address, e);
                        return Err((
                            StatusCode::INTERNAL_SERVER_ERROR,
                            Json(ErrorResponse {
                                error: "TRADES_SAVE_ERROR".to_string(),
                                message: format!("Failed to save trades for token {}: {}", token_address, e),
                                timestamp: Utc::now(),
                            }),
                        ));
                    }
                }
            }
            Err(e) => {
                error!("❌ Failed to fetch trades for token {} (after retry): {}", token_address, e);
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse {
                        error: "TRADES_FETCH_ERROR".to_string(),
                        message: format!("Failed to fetch trades for token {} after retry: {}", token_address, e),
                        timestamp: Utc::now(),
                    }),
                ));
            }
        }

        // Rate limit: 1 request per second (wait 1000ms between requests)
        if index < tokens_without_trades.len() - 1 {
            tokio::time::sleep(tokio::time::Duration::from_millis(1000)).await;
        }
    }

    info!("🎯 Trades collection completed: {} trades collected for {} tokens",
          trades_collected, trades_tokens_processed);

    let response = CollectResponse {
        success: true,
        message: format!(
            "Complete workflow: {} pump.fun tokens, {} Helius assets, {} tokens processed, {} tokens saved, {} trades collected for {} tokens (errors: {})",
            db_saved_count, helius_assets_collected, tokens_processed, tokens_saved_to_db,
            trades_collected, trades_tokens_processed, helius_errors + processing_errors
        ),
        tokens_count,
        db_saved_count,
        timestamp: Utc::now(),
    };

    info!(
        "Complete collection workflow finished: {} tokens saved, {} Helius assets collected, {} tokens processed, {} final tokens saved",
        db_saved_count, helius_assets_collected, tokens_processed, tokens_saved_to_db
    );

    Ok(Json(response))
}



pub async fn health_check(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>, (StatusCode, Json<ErrorResponse>)> {
    info!("Performing health check");

    // Check API connectivity
    let api_healthy = match state.client.health_check().await {
        Ok(healthy) => healthy,
        Err(e) => {
            warn!("API health check failed: {}", e);
            false
        }
    };

    // Check database connectivity
    let db_healthy = match state.database.health_check().await {
        Ok(healthy) => healthy,
        Err(e) => {
            warn!("Database health check failed: {}", e);
            false
        }
    };

    // Check Helius API connectivity
    let helius_healthy = match state.helius_client.health_check().await {
        Ok(healthy) => healthy,
        Err(e) => {
            warn!("Helius API health check failed: {}", e);
            false
        }
    };

    let overall_healthy = api_healthy && db_healthy && helius_healthy;
    let status_code = if overall_healthy {
        StatusCode::OK
    } else {
        StatusCode::SERVICE_UNAVAILABLE
    };

    let response = serde_json::json!({
        "status": if overall_healthy { "healthy" } else { "unhealthy" },
        "timestamp": Utc::now(),
        "checks": {
            "api": {
                "status": if api_healthy { "healthy" } else { "unhealthy" },
                "description": "Solana Tracker API connectivity"
            },
            "database": {
                "status": if db_healthy { "healthy" } else { "unhealthy" },
                "description": "PostgreSQL database connectivity"
            },
            "helius_api": {
                "status": if helius_healthy { "healthy" } else { "unhealthy" },
                "description": "Helius API connectivity"
            }
        }
    });

    if overall_healthy {
        info!("Health check passed");
        Ok(Json(response))
    } else {
        warn!("Health check failed");
        Err((
            status_code,
            Json(ErrorResponse {
                error: "HEALTH_CHECK_FAILED".to_string(),
                message: "One or more health checks failed".to_string(),
                timestamp: Utc::now(),
            }),
        ))
    }
}

pub async fn get_status() -> Json<serde_json::Value> {
    Json(serde_json::json!({
        "service": "SES Backend",
        "version": "1.0.0",
        "status": "running",
        "timestamp": Utc::now(),
        "endpoints": {
            "/collect": "POST - Complete workflow: collect tokens, fetch Helius assets, process in batches",
            "/health": "GET - Health check",
            "/status": "GET - Service status"
        }
    }))
}
