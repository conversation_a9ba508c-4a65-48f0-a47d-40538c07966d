use anyhow::{Context, Result};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::time::Duration;
use tracing::{info, warn, error, debug};

#[derive(Clone)]
pub struct HeliusClient {
    client: Client,
    api_key: String,
    base_url: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetAssetsByCreatorRequest {
    pub jsonrpc: String,
    pub id: String,
    pub method: String,
    pub params: GetAssetsByCreatorParams,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GetAssetsByCreatorParams {
    #[serde(rename = "creatorAddress")]
    pub creator_address: String,
    pub page: Option<u32>,
    pub limit: Option<u32>,
    #[serde(rename = "displayOptions")]
    pub display_options: Option<DisplayOptions>,
    #[serde(rename = "onlyVerified")]
    pub only_verified: Option<bool>,
    #[serde(rename = "sortBy")]
    pub sort_by: Option<SortOptions>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SortOptions {
    #[serde(rename = "sortBy")]
    pub sort_by: String,
    #[serde(rename = "sortDirection")]
    pub sort_direction: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DisplayOptions {
    #[serde(rename = "showFungible")]
    pub show_fungible: Option<bool>,
    #[serde(rename = "showNativeBalance")]
    pub show_native_balance: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HeliusResponse {
    pub jsonrpc: String,
    pub result: HeliusResult,
    pub id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HeliusResult {
    pub total: u32,
    pub limit: u32,
    pub page: Option<u32>,
    pub items: Vec<Value>, // Using Value to capture all asset data
}



impl HeliusClient {
    pub fn new(api_key: String, base_url: String) -> Result<Self> {
        let client = Client::builder()
            // No timeout - let Helius take as long as needed for complex sorting queries
            .user_agent("SES-Backend/1.0")
            .build()
            .context("Failed to create HTTP client for Helius")?;

        Ok(Self {
            client,
            api_key,
            base_url,
        })
    }

    pub async fn get_assets_by_creator(&self, creator_address: &str) -> Result<HeliusResult> {
        let url = format!("{}/?api-key={}", self.base_url, self.api_key);
        
        info!("Fetching assets for creator: {}", creator_address);

        let request_body = GetAssetsByCreatorRequest {
            jsonrpc: "2.0".to_string(),
            id: "get-assets-by-creator".to_string(),
            method: "getAssetsByCreator".to_string(),
            params: GetAssetsByCreatorParams {
                creator_address: creator_address.to_string(),
                page: Some(1),
                limit: Some(1000), // Max limit
                display_options: Some(DisplayOptions {
                    show_fungible: Some(true),
                    show_native_balance: Some(false),
                }),
                only_verified: None,
                sort_by: None,
            },
        };

        let response = self
            .client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&request_body)
            .send()
            .await
            .context("Failed to send request to Helius API")?;

        let status = response.status();
        info!("Helius API response status for {}: {}", creator_address, status);

        if !status.is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            
            error!("Helius API request failed with status {}: {}", status, error_text);
            
            return Err(anyhow::anyhow!(
                "Helius API request failed with status {}: {}", 
                status, 
                error_text
            ));
        }

        let response_text = response
            .text()
            .await
            .context("Failed to read Helius response body")?;

        // Parse the response
        match serde_json::from_str::<HeliusResponse>(&response_text) {
            Ok(helius_response) => {
                info!(
                    "Successfully fetched {} assets for creator {}",
                    helius_response.result.items.len(),
                    creator_address
                );
                Ok(helius_response.result)
            }
            Err(e) => {
                error!("Failed to parse Helius API response: {}", e);
                error!("Response body: {}", response_text);
                Err(anyhow::anyhow!("Failed to parse Helius API response: {}", e))
            }
        }
    }

    pub async fn get_recent_assets_by_creator(&self, creator_address: &str, limit: usize) -> Result<Vec<Value>> {
        let url = format!("{}/?api-key={}", self.base_url, self.api_key);
        let mut all_assets = Vec::new();
        let mut page = 1;
        let page_size = 1000; // Helius max per page

        info!("Fetching {} most recent assets for creator: {}", limit, creator_address);

        while all_assets.len() < limit {
            let remaining = limit - all_assets.len();
            let current_limit = std::cmp::min(remaining, page_size);

            debug!("🔍 DEBUG: Fetching page {} with limit {} for creator {}", page, current_limit, creator_address);

            let request_body = GetAssetsByCreatorRequest {
                jsonrpc: "2.0".to_string(),
                id: format!("get-recent-assets-{}-page-{}", creator_address, page),
                method: "getAssetsByCreator".to_string(),
                params: GetAssetsByCreatorParams {
                    creator_address: creator_address.to_string(),
                    page: Some(page),
                    limit: Some(current_limit as u32),
                    display_options: Some(DisplayOptions {
                        show_fungible: Some(true),
                        show_native_balance: Some(false),
                    }),
                    only_verified: Some(false),
                    sort_by: None, // Remove sorting for better performance
                },
            };

            let response = self
                .client
                .post(&url)
                .header("Content-Type", "application/json")
                .json(&request_body)
                .send()
                .await
                .context("Failed to send request to Helius API")?;

            let status = response.status();
            if !status.is_success() {
                let error_text = response.text().await.unwrap_or_default();
                error!("Helius API request failed with status {}: {}", status, error_text);
                return Err(anyhow::anyhow!(
                    "Helius API request failed with status {}: {}",
                    status,
                    error_text
                ));
            }

            let response_text = response.text().await?;

            match serde_json::from_str::<HeliusResponse>(&response_text) {
                Ok(helius_response) => {
                    let page_assets = helius_response.result.items;
                    debug!("🔍 DEBUG: Page {} returned {} assets for creator {}", page, page_assets.len(), creator_address);

                    if page_assets.is_empty() {
                        debug!("🔍 DEBUG: No more assets available for creator {}, stopping pagination", creator_address);
                        break;
                    }

                    all_assets.extend(page_assets);
                    page += 1;

                    // Add delay between pages to respect rate limits
                    if all_assets.len() < limit {
                        tokio::time::sleep(tokio::time::Duration::from_millis(101)).await;
                    }
                }
                Err(e) => {
                    error!("Failed to parse Helius response: {}", e);
                    error!("Response body: {}", response_text);
                    return Err(anyhow::anyhow!("Failed to parse Helius response: {}", e));
                }
            }
        }

        // Truncate to exact limit if we got more
        all_assets.truncate(limit);

        info!(
            "Successfully fetched {} recent assets for creator {} (requested: {}, pages: {})",
            all_assets.len(),
            creator_address,
            limit,
            page - 1
        );

        Ok(all_assets)
    }





    pub async fn health_check(&self) -> Result<bool> {
        // Simple health check - try to get assets for a known creator with limit 1
        let url = format!("{}/?api-key={}", self.base_url, self.api_key);
        
        let request_body = GetAssetsByCreatorRequest {
            jsonrpc: "2.0".to_string(),
            id: "health-check".to_string(),
            method: "getAssetsByCreator".to_string(),
            params: GetAssetsByCreatorParams {
                creator_address: "********************************".to_string(), // System program
                page: Some(1),
                limit: Some(1),
                display_options: None,
                only_verified: None,
                sort_by: None,
            },
        };

        match self
            .client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&request_body)
            .timeout(Duration::from_secs(10))
            .send()
            .await
        {
            Ok(response) => {
                let is_healthy = response.status().is_success();
                if is_healthy {
                    info!("Helius API health check passed");
                } else {
                    warn!("Helius API health check failed with status: {}", response.status());
                }
                Ok(is_healthy)
            }
            Err(e) => {
                warn!("Helius API health check failed: {}", e);
                Ok(false)
            }
        }
    }
}
