#!/usr/bin/env python3
"""
🚀 Comprehensive Database Analysis for Solana Token Data
Analyzes ALL fields in the tokens database with advanced ML and statistical techniques.
"""

import os
import sys
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import psycopg2
from sqlalchemy import create_engine, text
from datetime import datetime, timedelta
import warnings
from typing import Dict, List, Any, Optional
from collections import Counter
import re
from wordcloud import WordCloud
import networkx as nx
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.cluster import KMeans, DBSCAN
from sklearn.decomposition import PCA
from sklearn.ensemble import IsolationForest
from sklearn.feature_extraction.text import TfidfVectorizer
from scipy import stats
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class SolanaTokenAnalyzer:
    """Comprehensive analyzer for Solana token database."""
    
    def __init__(self, database_url: Optional[str] = None):
        """Initialize the analyzer with database connection."""
        self.database_url = database_url or os.getenv('DATABASE_URL', 'postgresql://postgres@localhost:5432/ses_db')
        self.engine = None
        self.df = None
        self.raw_data_df = None
        
    def connect_database(self) -> bool:
        """Establish database connection."""
        try:
            self.engine = create_engine(self.database_url)
            print("✅ Database connection established successfully!")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to database: {e}")
            return False
    
    def load_data(self) -> bool:
        """Load all data from the tokens table."""
        try:
            # Load main table data
            query = """
            SELECT 
                id,
                token_address,
                creator_address,
                raw_data,
                created_at,
                updated_at
            FROM tokens 
            ORDER BY created_at DESC
            """
            
            self.df = pd.read_sql(query, self.engine)
            print(f"✅ Loaded {len(self.df)} tokens from database")
            
            # Parse and normalize raw_data JSONB field
            self._parse_raw_data()
            
            return True
        except Exception as e:
            print(f"❌ Failed to load data: {e}")
            return False
    
    def _parse_raw_data(self):
        """Parse and normalize the raw_data JSONB field into structured columns."""
        print("🔍 Parsing raw_data JSONB field...")
        
        # Extract all possible fields from raw_data
        all_fields = set()
        parsed_data = []
        
        for idx, row in self.df.iterrows():
            try:
                raw_data = row['raw_data']
                if isinstance(raw_data, str):
                    raw_data = json.loads(raw_data)
                
                # Flatten nested JSON structure
                flattened = self._flatten_json(raw_data)
                all_fields.update(flattened.keys())
                parsed_data.append(flattened)
                
            except Exception as e:
                print(f"⚠️  Error parsing row {idx}: {e}")
                parsed_data.append({})
        
        # Create DataFrame with all parsed fields
        self.raw_data_df = pd.DataFrame(parsed_data)
        
        # Combine with main DataFrame
        self.df = pd.concat([
            self.df.drop('raw_data', axis=1),
            self.raw_data_df
        ], axis=1)
        
        print(f"✅ Parsed {len(all_fields)} unique fields from raw_data")
        print(f"📊 Total columns in dataset: {len(self.df.columns)}")
    
    def _flatten_json(self, data: Dict, parent_key: str = '', sep: str = '_') -> Dict:
        """Recursively flatten nested JSON structure."""
        items = []
        
        if isinstance(data, dict):
            for k, v in data.items():
                new_key = f"{parent_key}{sep}{k}" if parent_key else k
                
                if isinstance(v, dict):
                    items.extend(self._flatten_json(v, new_key, sep=sep).items())
                elif isinstance(v, list):
                    # Handle lists by converting to string or extracting specific elements
                    if len(v) > 0 and isinstance(v[0], dict):
                        # If list contains dicts, flatten first element
                        items.extend(self._flatten_json(v[0], f"{new_key}_0", sep=sep).items())
                    else:
                        items.append((new_key, str(v)))
                else:
                    items.append((new_key, v))
        
        return dict(items)
    
    def generate_comprehensive_report(self):
        """Generate a comprehensive analysis report of all database fields."""
        print("\n" + "="*80)
        print("🚀 COMPREHENSIVE SOLANA TOKEN DATABASE ANALYSIS")
        print("="*80)
        
        # Basic dataset information
        self._basic_dataset_info()
        
        # Analyze all columns
        self._analyze_all_columns()
        
        # Time-based analysis
        self._time_based_analysis()
        
        # Creator analysis
        self._creator_analysis()
        
        # Token characteristics analysis
        self._token_characteristics_analysis()
        
        # Advanced analytics
        self._advanced_analytics()
        
        # Generate visualizations
        self._create_visualizations()
        
        print("\n✅ Comprehensive analysis completed!")
        print("📊 Check the generated plots and analysis results above.")
    
    def _basic_dataset_info(self):
        """Analyze basic dataset information."""
        print("\n📊 BASIC DATASET INFORMATION")
        print("-" * 50)
        
        print(f"Total tokens: {len(self.df):,}")
        print(f"Total columns: {len(self.df.columns)}")
        print(f"Memory usage: {self.df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        # Date range
        if 'created_at' in self.df.columns:
            date_range = self.df['created_at'].max() - self.df['created_at'].min()
            print(f"Date range: {date_range.days} days")
            print(f"Earliest token: {self.df['created_at'].min()}")
            print(f"Latest token: {self.df['created_at'].max()}")
        
        # Missing data analysis
        print(f"\n🔍 Missing Data Analysis:")
        missing_data = self.df.isnull().sum()
        missing_pct = (missing_data / len(self.df)) * 100

        for col in missing_data[missing_data > 0].head(10).index:
            print(f"  {col}: {missing_data[col]:,} ({missing_pct[col]:.1f}%)")

    def _analyze_all_columns(self):
        """Analyze all columns in the dataset."""
        print("\n📋 DETAILED COLUMN ANALYSIS")
        print("-" * 50)

        for col in self.df.columns:
            print(f"\n🔍 Column: {col}")
            print(f"  Type: {self.df[col].dtype}")
            print(f"  Non-null count: {self.df[col].count():,}")
            print(f"  Unique values: {self.df[col].nunique():,}")

            if self.df[col].dtype in ['object', 'string']:
                # String/object analysis
                if self.df[col].nunique() < 20:
                    value_counts = self.df[col].value_counts().head(5)
                    print(f"  Top values: {dict(value_counts)}")
                else:
                    print(f"  Sample values: {list(self.df[col].dropna().head(3))}")

                # String length analysis
                if self.df[col].dtype == 'object':
                    str_lengths = self.df[col].astype(str).str.len()
                    print(f"  String length - Min: {str_lengths.min()}, Max: {str_lengths.max()}, Avg: {str_lengths.mean():.1f}")

            elif self.df[col].dtype in ['int64', 'float64', 'int32', 'float32']:
                # Numerical analysis
                desc = self.df[col].describe()
                print(f"  Min: {desc['min']:.2f}, Max: {desc['max']:.2f}")
                print(f"  Mean: {desc['mean']:.2f}, Median: {desc['50%']:.2f}")
                print(f"  Std: {desc['std']:.2f}")

                # Check for outliers
                Q1 = desc['25%']
                Q3 = desc['75%']
                IQR = Q3 - Q1
                outliers = self.df[(self.df[col] < Q1 - 1.5*IQR) | (self.df[col] > Q3 + 1.5*IQR)][col]
                print(f"  Outliers: {len(outliers)} ({len(outliers)/len(self.df)*100:.1f}%)")

            elif 'datetime' in str(self.df[col].dtype):
                # DateTime analysis
                print(f"  Date range: {self.df[col].min()} to {self.df[col].max()}")
                if self.df[col].count() > 1:
                    date_diff = self.df[col].max() - self.df[col].min()
                    print(f"  Span: {date_diff.days} days")

    def _time_based_analysis(self):
        """Analyze time-based patterns in the data."""
        print("\n⏰ TIME-BASED ANALYSIS")
        print("-" * 50)

        if 'created_at' not in self.df.columns:
            print("❌ No created_at column found for time analysis")
            return

        # Convert to datetime if needed
        self.df['created_at'] = pd.to_datetime(self.df['created_at'])

        # Daily token creation
        daily_counts = self.df.groupby(self.df['created_at'].dt.date).size()
        print(f"📅 Daily token creation:")
        print(f"  Average per day: {daily_counts.mean():.1f}")
        print(f"  Peak day: {daily_counts.max()} tokens on {daily_counts.idxmax()}")
        print(f"  Lowest day: {daily_counts.min()} tokens on {daily_counts.idxmin()}")

        # Hourly patterns
        hourly_counts = self.df.groupby(self.df['created_at'].dt.hour).size()
        peak_hour = hourly_counts.idxmax()
        print(f"  Peak hour: {peak_hour}:00 with {hourly_counts.max()} tokens")

        # Weekly patterns
        weekly_counts = self.df.groupby(self.df['created_at'].dt.day_name()).size()
        peak_day = weekly_counts.idxmax()
        print(f"  Peak day of week: {peak_day} with {weekly_counts.max()} tokens")

        # Recent activity
        recent_7d = self.df[self.df['created_at'] > self.df['created_at'].max() - timedelta(days=7)]
        print(f"  Tokens in last 7 days: {len(recent_7d)}")

    def _creator_analysis(self):
        """Analyze creator patterns and statistics."""
        print("\n👥 CREATOR ANALYSIS")
        print("-" * 50)

        if 'creator_address' not in self.df.columns:
            print("❌ No creator_address column found")
            return

        # Basic creator stats
        total_creators = self.df['creator_address'].nunique()
        tokens_with_creators = self.df['creator_address'].count()

        print(f"Total unique creators: {total_creators:,}")
        print(f"Tokens with creators: {tokens_with_creators:,}")
        print(f"Tokens without creators: {len(self.df) - tokens_with_creators:,}")

        # Creator productivity
        creator_counts = self.df['creator_address'].value_counts()
        print(f"\n🏆 Top creators by token count:")
        for i, (creator, count) in enumerate(creator_counts.head(10).items(), 1):
            print(f"  {i}. {creator}: {count} tokens")

        # Creator distribution analysis
        print(f"\n📊 Creator productivity distribution:")
        print(f"  Creators with 1 token: {(creator_counts == 1).sum():,}")
        print(f"  Creators with 2-5 tokens: {((creator_counts >= 2) & (creator_counts <= 5)).sum():,}")
        print(f"  Creators with 6-10 tokens: {((creator_counts >= 6) & (creator_counts <= 10)).sum():,}")
        print(f"  Creators with 10+ tokens: {(creator_counts > 10).sum():,}")

        # Power creators (top 1%)
        top_1_pct_threshold = int(total_creators * 0.01)
        power_creators = creator_counts.head(top_1_pct_threshold)
        power_creator_tokens = power_creators.sum()
        print(f"  Top 1% creators ({top_1_pct_threshold}) created {power_creator_tokens} tokens ({power_creator_tokens/len(self.df)*100:.1f}%)")

    def _token_characteristics_analysis(self):
        """Analyze token characteristics from raw_data fields."""
        print("\n🪙 TOKEN CHARACTERISTICS ANALYSIS")
        print("-" * 50)

        # Analyze token-specific fields
        token_fields = [col for col in self.df.columns if 'token' in col.lower()]

        for field in token_fields[:10]:  # Limit to first 10 token fields
            if field in self.df.columns and self.df[field].count() > 0:
                print(f"\n🔍 {field}:")
                if self.df[field].dtype == 'object':
                    unique_vals = self.df[field].nunique()
                    print(f"  Unique values: {unique_vals}")
                    if unique_vals < 20:
                        value_counts = self.df[field].value_counts().head(5)
                        for val, count in value_counts.items():
                            print(f"    {val}: {count}")
                else:
                    desc = self.df[field].describe()
                    print(f"  Mean: {desc['mean']:.2f}, Median: {desc['50%']:.2f}")

        # Look for price/market cap related fields
        price_fields = [col for col in self.df.columns if any(keyword in col.lower() for keyword in ['price', 'market', 'cap', 'volume', 'liquidity'])]

        if price_fields:
            print(f"\n💰 Price/Market related fields found: {len(price_fields)}")
            for field in price_fields[:5]:
                if self.df[field].dtype in ['int64', 'float64'] and self.df[field].count() > 0:
                    desc = self.df[field].describe()
                    print(f"  {field}: Min: {desc['min']:.6f}, Max: {desc['max']:.6f}, Mean: {desc['mean']:.6f}")

    def _advanced_analytics(self):
        """Perform advanced analytics including clustering and anomaly detection."""
        print("\n🧠 ADVANCED ANALYTICS")
        print("-" * 50)

        # Prepare numerical data for ML analysis
        numerical_cols = self.df.select_dtypes(include=[np.number]).columns

        if len(numerical_cols) < 2:
            print("❌ Insufficient numerical data for advanced analytics")
            return

        # Remove ID columns and columns with too many nulls
        analysis_cols = [col for col in numerical_cols
                        if not col.lower().endswith('_id')
                        and self.df[col].count() > len(self.df) * 0.5]

        if len(analysis_cols) < 2:
            print("❌ Insufficient clean numerical data for advanced analytics")
            return

        print(f"📊 Using {len(analysis_cols)} numerical columns for analysis")

        # Prepare data
        analysis_data = self.df[analysis_cols].fillna(self.df[analysis_cols].median())

        # Standardize data
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(analysis_data)

        # Clustering analysis
        try:
            kmeans = KMeans(n_clusters=5, random_state=42, n_init=10)
            clusters = kmeans.fit_predict(scaled_data)

            print(f"🎯 K-Means Clustering (5 clusters):")
            cluster_counts = pd.Series(clusters).value_counts().sort_index()
            for cluster, count in cluster_counts.items():
                print(f"  Cluster {cluster}: {count} tokens ({count/len(self.df)*100:.1f}%)")
        except Exception as e:
            print(f"⚠️  Clustering failed: {e}")

        # Anomaly detection
        try:
            iso_forest = IsolationForest(contamination=0.1, random_state=42)
            anomalies = iso_forest.fit_predict(scaled_data)
            anomaly_count = (anomalies == -1).sum()

            print(f"🚨 Anomaly Detection:")
            print(f"  Anomalous tokens detected: {anomaly_count} ({anomaly_count/len(self.df)*100:.1f}%)")
        except Exception as e:
            print(f"⚠️  Anomaly detection failed: {e}")

        # Correlation analysis
        try:
            correlation_matrix = analysis_data.corr()
            high_corr_pairs = []

            for i in range(len(correlation_matrix.columns)):
                for j in range(i+1, len(correlation_matrix.columns)):
                    corr_val = correlation_matrix.iloc[i, j]
                    if abs(corr_val) > 0.7:
                        high_corr_pairs.append((
                            correlation_matrix.columns[i],
                            correlation_matrix.columns[j],
                            corr_val
                        ))

            print(f"🔗 High Correlations (|r| > 0.7): {len(high_corr_pairs)} pairs")
            for col1, col2, corr in high_corr_pairs[:5]:
                print(f"  {col1} ↔ {col2}: {corr:.3f}")
        except Exception as e:
            print(f"⚠️  Correlation analysis failed: {e}")

    def _create_visualizations(self):
        """Create comprehensive visualizations of the data."""
        print("\n📊 CREATING VISUALIZATIONS")
        print("-" * 50)

        # Set up the plotting environment
        plt.rcParams['figure.figsize'] = (12, 8)

        # 1. Time series analysis
        if 'created_at' in self.df.columns:
            self._plot_time_series()

        # 2. Creator analysis plots
        if 'creator_address' in self.df.columns:
            self._plot_creator_analysis()

        # 3. Numerical field distributions
        self._plot_numerical_distributions()

        # 4. Correlation heatmap
        self._plot_correlation_heatmap()

        # 5. Text analysis (if applicable)
        self._plot_text_analysis()

        print("✅ All visualizations created and saved!")

    def _plot_time_series(self):
        """Create time series visualizations."""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Time Series Analysis', fontsize=16, fontweight='bold')

            # Daily token creation
            daily_counts = self.df.groupby(self.df['created_at'].dt.date).size()
            axes[0, 0].plot(daily_counts.index, daily_counts.values, marker='o', linewidth=2)
            axes[0, 0].set_title('Daily Token Creation')
            axes[0, 0].set_xlabel('Date')
            axes[0, 0].set_ylabel('Number of Tokens')
            axes[0, 0].tick_params(axis='x', rotation=45)

            # Hourly distribution
            hourly_counts = self.df.groupby(self.df['created_at'].dt.hour).size()
            axes[0, 1].bar(hourly_counts.index, hourly_counts.values, color='skyblue')
            axes[0, 1].set_title('Hourly Distribution')
            axes[0, 1].set_xlabel('Hour of Day')
            axes[0, 1].set_ylabel('Number of Tokens')

            # Day of week distribution
            weekly_counts = self.df.groupby(self.df['created_at'].dt.day_name()).size()
            axes[1, 0].bar(weekly_counts.index, weekly_counts.values, color='lightgreen')
            axes[1, 0].set_title('Day of Week Distribution')
            axes[1, 0].set_xlabel('Day of Week')
            axes[1, 0].set_ylabel('Number of Tokens')
            axes[1, 0].tick_params(axis='x', rotation=45)

            # Cumulative tokens over time
            cumulative = self.df.groupby(self.df['created_at'].dt.date).size().cumsum()
            axes[1, 1].plot(cumulative.index, cumulative.values, color='red', linewidth=2)
            axes[1, 1].set_title('Cumulative Token Growth')
            axes[1, 1].set_xlabel('Date')
            axes[1, 1].set_ylabel('Cumulative Tokens')
            axes[1, 1].tick_params(axis='x', rotation=45)

            plt.tight_layout()
            plt.savefig('outputs/visualizations/time_series_analysis.png', dpi=300, bbox_inches='tight')
            plt.show()

        except Exception as e:
            print(f"⚠️  Time series plotting failed: {e}")

    def _plot_creator_analysis(self):
        """Create creator analysis visualizations."""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('Creator Analysis', fontsize=16, fontweight='bold')

            # Top creators
            creator_counts = self.df['creator_address'].value_counts().head(20)
            axes[0, 0].barh(range(len(creator_counts)), creator_counts.values)
            axes[0, 0].set_yticks(range(len(creator_counts)))
            axes[0, 0].set_yticklabels([f"{addr[:8]}..." for addr in creator_counts.index])
            axes[0, 0].set_title('Top 20 Creators by Token Count')
            axes[0, 0].set_xlabel('Number of Tokens')

            # Creator productivity distribution
            productivity_dist = creator_counts.value_counts().sort_index()
            axes[0, 1].bar(productivity_dist.index, productivity_dist.values, color='orange')
            axes[0, 1].set_title('Creator Productivity Distribution')
            axes[0, 1].set_xlabel('Tokens per Creator')
            axes[0, 1].set_ylabel('Number of Creators')
            axes[0, 1].set_yscale('log')

            # Creator concentration (Pareto analysis)
            creator_cumsum = creator_counts.cumsum()
            creator_pct = creator_cumsum / creator_cumsum.iloc[-1] * 100
            axes[1, 0].plot(range(1, len(creator_pct) + 1), creator_pct.values)
            axes[1, 0].set_title('Creator Concentration (Pareto)')
            axes[1, 0].set_xlabel('Creator Rank')
            axes[1, 0].set_ylabel('Cumulative % of Tokens')
            axes[1, 0].grid(True, alpha=0.3)

            # Tokens with/without creators
            creator_status = ['With Creator', 'Without Creator']
            creator_counts_status = [
                self.df['creator_address'].count(),
                len(self.df) - self.df['creator_address'].count()
            ]
            axes[1, 1].pie(creator_counts_status, labels=creator_status, autopct='%1.1f%%', startangle=90)
            axes[1, 1].set_title('Tokens by Creator Status')

            plt.tight_layout()
            plt.savefig('outputs/visualizations/creator_analysis.png', dpi=300, bbox_inches='tight')
            plt.show()

        except Exception as e:
            print(f"⚠️  Creator analysis plotting failed: {e}")

    def _plot_numerical_distributions(self):
        """Plot distributions of numerical fields."""
        try:
            numerical_cols = self.df.select_dtypes(include=[np.number]).columns

            # Filter out ID columns and columns with too many nulls
            plot_cols = [col for col in numerical_cols
                        if not col.lower().endswith('_id')
                        and self.df[col].count() > len(self.df) * 0.1][:9]  # Max 9 plots

            if len(plot_cols) == 0:
                print("❌ No suitable numerical columns for distribution plots")
                return

            n_cols = min(3, len(plot_cols))
            n_rows = (len(plot_cols) + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
            fig.suptitle('Numerical Field Distributions', fontsize=16, fontweight='bold')

            if n_rows == 1:
                axes = [axes] if n_cols == 1 else axes
            else:
                axes = axes.flatten()

            for i, col in enumerate(plot_cols):
                if i < len(axes):
                    data = self.df[col].dropna()
                    if len(data) > 0:
                        axes[i].hist(data, bins=50, alpha=0.7, edgecolor='black')
                        axes[i].set_title(f'{col}\n(n={len(data):,})')
                        axes[i].set_xlabel('Value')
                        axes[i].set_ylabel('Frequency')

                        # Add statistics
                        mean_val = data.mean()
                        median_val = data.median()
                        axes[i].axvline(mean_val, color='red', linestyle='--', label=f'Mean: {mean_val:.2f}')
                        axes[i].axvline(median_val, color='green', linestyle='--', label=f'Median: {median_val:.2f}')
                        axes[i].legend()

            # Hide empty subplots
            for i in range(len(plot_cols), len(axes)):
                axes[i].set_visible(False)

            plt.tight_layout()
            plt.savefig('outputs/visualizations/numerical_distributions.png', dpi=300, bbox_inches='tight')
            plt.show()

        except Exception as e:
            print(f"⚠️  Numerical distribution plotting failed: {e}")

    def _plot_correlation_heatmap(self):
        """Create correlation heatmap for numerical fields."""
        try:
            numerical_cols = self.df.select_dtypes(include=[np.number]).columns

            # Filter columns
            corr_cols = [col for col in numerical_cols
                        if not col.lower().endswith('_id')
                        and self.df[col].count() > len(self.df) * 0.5][:20]  # Max 20 columns

            if len(corr_cols) < 2:
                print("❌ Insufficient data for correlation heatmap")
                return

            correlation_matrix = self.df[corr_cols].corr()

            plt.figure(figsize=(12, 10))
            mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
            sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm',
                       center=0, square=True, fmt='.2f', cbar_kws={"shrink": .8})
            plt.title('Correlation Heatmap of Numerical Fields', fontsize=16, fontweight='bold')
            plt.tight_layout()
            plt.savefig('outputs/visualizations/correlation_heatmap.png', dpi=300, bbox_inches='tight')
            plt.show()

        except Exception as e:
            print(f"⚠️  Correlation heatmap plotting failed: {e}")

    def _plot_text_analysis(self):
        """Analyze and visualize text fields."""
        try:
            # Find text fields that might contain token names, symbols, or descriptions
            text_fields = []
            for col in self.df.columns:
                if (self.df[col].dtype == 'object' and
                    col.lower() not in ['id', 'address', 'creator_address', 'token_address'] and
                    self.df[col].count() > 10):
                    text_fields.append(col)

            if not text_fields:
                print("❌ No suitable text fields found for analysis")
                return

            # Analyze first suitable text field
            text_col = text_fields[0]
            text_data = self.df[text_col].dropna().astype(str)

            if len(text_data) == 0:
                return

            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'Text Analysis: {text_col}', fontsize=16, fontweight='bold')

            # Text length distribution
            text_lengths = text_data.str.len()
            axes[0, 0].hist(text_lengths, bins=50, alpha=0.7, edgecolor='black')
            axes[0, 0].set_title('Text Length Distribution')
            axes[0, 0].set_xlabel('Character Count')
            axes[0, 0].set_ylabel('Frequency')

            # Word count distribution
            word_counts = text_data.str.split().str.len()
            axes[0, 1].hist(word_counts, bins=30, alpha=0.7, edgecolor='black', color='orange')
            axes[0, 1].set_title('Word Count Distribution')
            axes[0, 1].set_xlabel('Word Count')
            axes[0, 1].set_ylabel('Frequency')

            # Most common values
            value_counts = text_data.value_counts().head(20)
            if len(value_counts) > 0:
                axes[1, 0].barh(range(len(value_counts)), value_counts.values)
                axes[1, 0].set_yticks(range(len(value_counts)))
                axes[1, 0].set_yticklabels([str(val)[:20] + '...' if len(str(val)) > 20 else str(val)
                                          for val in value_counts.index])
                axes[1, 0].set_title('Most Common Values')
                axes[1, 0].set_xlabel('Frequency')

            # Character frequency (for short text fields)
            if text_lengths.mean() < 50:
                all_chars = ''.join(text_data).lower()
                char_freq = Counter(all_chars)
                common_chars = dict(char_freq.most_common(20))

                axes[1, 1].bar(common_chars.keys(), common_chars.values())
                axes[1, 1].set_title('Character Frequency')
                axes[1, 1].set_xlabel('Character')
                axes[1, 1].set_ylabel('Frequency')
            else:
                axes[1, 1].text(0.5, 0.5, 'Text too long for\ncharacter analysis',
                              ha='center', va='center', transform=axes[1, 1].transAxes)
                axes[1, 1].set_title('Character Analysis')

            plt.tight_layout()
            plt.savefig('outputs/visualizations/text_analysis.png', dpi=300, bbox_inches='tight')
            plt.show()

        except Exception as e:
            print(f"⚠️  Text analysis plotting failed: {e}")


def main():
    """Main execution function."""
    print("🚀 Starting Comprehensive Solana Token Database Analysis")
    print("=" * 80)

    # Initialize analyzer
    analyzer = SolanaTokenAnalyzer()

    # Connect to database
    if not analyzer.connect_database():
        print("❌ Failed to connect to database. Please check your DATABASE_URL.")
        return

    # Load data
    if not analyzer.load_data():
        print("❌ Failed to load data from database.")
        return

    # Generate comprehensive report
    analyzer.generate_comprehensive_report()

    print("\n" + "=" * 80)
    print("🎉 Analysis completed successfully!")
    print("📁 Check the generated PNG files for visualizations:")
    print("   - time_series_analysis.png")
    print("   - creator_analysis.png")
    print("   - numerical_distributions.png")
    print("   - correlation_heatmap.png")
    print("   - text_analysis.png")
    print("=" * 80)


if __name__ == "__main__":
    main()
