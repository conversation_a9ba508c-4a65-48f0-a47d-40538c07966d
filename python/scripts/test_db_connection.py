#!/usr/bin/env python3
"""
Quick database connection test and basic data preview
"""

import os
import psycopg2
from sqlalchemy import create_engine, text
import pandas as pd
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_connection():
    """Test database connection and show basic info."""
    
    database_url = os.getenv('DATABASE_URL', 'postgresql://postgres@localhost:5432/ses_db')
    print(f"🔗 Testing connection to: {database_url}")
    
    try:
        # Test with SQLAlchemy
        engine = create_engine(database_url)
        
        # Test basic connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ Database connection successful!")
        
        # Get table info
        print("\n📊 Database Information:")
        
        # Count total tokens
        count_query = "SELECT COUNT(*) as total_tokens FROM tokens"
        df_count = pd.read_sql(count_query, engine)
        total_tokens = df_count['total_tokens'].iloc[0]
        print(f"   Total tokens: {total_tokens:,}")
        
        if total_tokens > 0:
            # Get column info
            columns_query = """
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'tokens'
            ORDER BY ordinal_position
            """
            df_columns = pd.read_sql(columns_query, engine)
            print(f"   Table columns: {len(df_columns)}")
            
            for _, row in df_columns.iterrows():
                nullable = "NULL" if row['is_nullable'] == 'YES' else "NOT NULL"
                print(f"     - {row['column_name']}: {row['data_type']} ({nullable})")
            
            # Sample data
            print("\n🔍 Sample Data (first 3 rows):")
            sample_query = """
            SELECT 
                token_address,
                creator_address,
                created_at,
                CASE 
                    WHEN raw_data IS NOT NULL THEN 'Has data'
                    ELSE 'No data'
                END as raw_data_status
            FROM tokens 
            ORDER BY created_at DESC 
            LIMIT 3
            """
            df_sample = pd.read_sql(sample_query, engine)
            
            for i, row in df_sample.iterrows():
                print(f"   Row {i+1}:")
                print(f"     Token: {row['token_address']}")
                print(f"     Creator: {row['creator_address'] or 'None'}")
                print(f"     Created: {row['created_at']}")
                print(f"     Raw data: {row['raw_data_status']}")
            
            # Check raw_data structure
            print("\n🔍 Raw Data Structure Analysis:")
            raw_data_query = """
            SELECT raw_data 
            FROM tokens 
            WHERE raw_data IS NOT NULL 
            LIMIT 1
            """
            df_raw = pd.read_sql(raw_data_query, engine)
            
            if len(df_raw) > 0:
                import json
                raw_data = df_raw['raw_data'].iloc[0]
                if isinstance(raw_data, str):
                    raw_data = json.loads(raw_data)
                
                def count_keys(obj, prefix=""):
                    count = 0
                    if isinstance(obj, dict):
                        for key, value in obj.items():
                            count += 1
                            if isinstance(value, dict):
                                count += count_keys(value, f"{prefix}{key}.")
                            elif isinstance(value, list) and len(value) > 0 and isinstance(value[0], dict):
                                count += count_keys(value[0], f"{prefix}{key}[0].")
                    return count
                
                total_fields = count_keys(raw_data)
                print(f"   Estimated total fields in raw_data: {total_fields}")
                
                # Show top-level keys
                if isinstance(raw_data, dict):
                    print(f"   Top-level keys: {list(raw_data.keys())}")
        
        else:
            print("   ⚠️  No tokens found in database")
            
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("\n💡 Troubleshooting tips:")
        print("   1. Make sure PostgreSQL is running")
        print("   2. Check your DATABASE_URL in .env file")
        print("   3. Verify database exists and has data")
        print("   4. Check database permissions")
        return False

if __name__ == "__main__":
    print("🧪 Database Connection Test")
    print("=" * 40)
    
    success = test_connection()
    
    if success:
        print("\n✅ Database test completed successfully!")
        print("🚀 You can now run the full analysis with: ./run_analysis.sh")
    else:
        print("\n❌ Database test failed. Please fix the connection issues first.")
