# 🚀 SES - Solana Enhanced Scanner Configuration

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/ses_db

# API Keys
HELIUS_API_KEY=your_helius_api_key_here
SOLANA_TRACKER_API_KEY=your_solana_tracker_api_key_here

# Server Configuration
SERVER_HOST=127.0.0.1
SERVER_PORT=3000

# Logging
RUST_LOG=info
LOG_LEVEL=info

# Analysis Configuration
ANALYSIS_BATCH_SIZE=1000
ML_MODEL_VERSION=v1.0

# Feature Flags
ENABLE_REAL_TIME_ANALYSIS=true
ENABLE_ANOMALY_DETECTION=true
ENABLE_MIGRATION_PREDICTION=true

# Rate Limiting
API_RATE_LIMIT_PER_MINUTE=60
MAX_CONCURRENT_REQUESTS=10

# Data Retention
DATA_RETENTION_DAYS=90
CLEANUP_INTERVAL_HOURS=24

# Monitoring
HEALTH_CHECK_INTERVAL_SECONDS=30
METRICS_EXPORT_INTERVAL_SECONDS=60
